"""
AI Question Answering for Voice Assistant
Integrates with Grok API and OpenAI GPT-4 as fallback
"""

import requests
import json
import logging
from typing import Optional, Dict, Any, List
from utils.config import config

logger = logging.getLogger(__name__)

class AIAssistant:
    """Handles AI-powered question answering and conversation"""
    
    def __init__(self):
        """Initialize AI assistant with API clients"""
        self.api_keys = config.api_keys
        self.assistant_name = config.voice_settings['assistant_name']
        
        # OpenAI functionality removed - using Grok as primary AI service
        
        # Grok API configuration
        self.grok_api_url = "https://api.x.ai/v1/chat/completions"
        self.grok_headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_keys['grok']}"
        } if self.api_keys['grok'] else None
        
        # Conversation context
        self.conversation_history = []
        self.max_history_length = 10
        
        # System prompt
        self.system_prompt = self._create_system_prompt()
    
    def _create_system_prompt(self) -> str:
        """Create system prompt for the AI assistant"""
        return f"""You are {self.assistant_name}, an advanced AI voice assistant. You are helpful, friendly, and conversational.

Key guidelines:
- Keep responses concise and natural for speech (2-3 sentences max unless specifically asked for details)
- Be conversational and engaging
- Avoid using special characters, code blocks, or markdown in responses
- If asked about technical topics, explain in simple terms
- Always be helpful and try to provide useful information
- If you don't know something, admit it honestly
- Remember this is a voice conversation, so format responses for speaking

Current context: You are running on a Windows computer and can help with various tasks including:
- Answering questions
- Providing information
- Helping with computer tasks
- General conversation

Respond naturally as if speaking to a friend."""
    
    def ask_question(self, question: str, user_emotion: Optional[str] = None) -> str:
        """
        Ask a question to the AI and get a response
        
        Args:
            question: User's question
            user_emotion: Detected emotion from speech (optional)
            
        Returns:
            AI response text
        """
        if not question or not question.strip():
            return "I didn't catch that. Could you please repeat your question?"
        
        # Add emotion context if available
        if user_emotion and user_emotion != 'neutral':
            emotion_context = f" (User seems {user_emotion})"
            question_with_context = question + emotion_context
        else:
            question_with_context = question
        
        # Try Grok first, then OpenAI as fallback
        response = self._try_grok(question_with_context)
        if not response:
            response = self._try_openai(question_with_context)
        
        if not response:
            response = "I'm sorry, I'm having trouble connecting to my AI services right now. Please try again later."
        
        # Update conversation history
        self._update_conversation_history(question, response)
        
        return self._clean_response(response)
    
    def _try_grok(self, question: str) -> Optional[str]:
        """Try to get response from Grok API"""
        if not self.grok_headers:
            logger.debug("Grok API key not available")
            return None
        
        try:
            # Prepare messages with conversation history
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add recent conversation history
            for entry in self.conversation_history[-5:]:  # Last 5 exchanges
                messages.append({"role": "user", "content": entry["question"]})
                messages.append({"role": "assistant", "content": entry["response"]})
            
            # Add current question
            messages.append({"role": "user", "content": question})
            
            payload = {
                "model": "grok-beta",
                "messages": messages,
                "max_tokens": 150,
                "temperature": 0.7,
                "stream": False
            }
            
            logger.info("Sending request to Grok API")
            response = requests.post(
                self.grok_api_url,
                headers=self.grok_headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    answer = result['choices'][0]['message']['content'].strip()
                    logger.info("Received response from Grok API")
                    return answer
                else:
                    logger.warning("No choices in Grok API response")
            else:
                logger.error(f"Grok API error: {response.status_code} - {response.text}")
            
        except requests.exceptions.Timeout:
            logger.error("Grok API request timed out")
        except requests.exceptions.RequestException as e:
            logger.error(f"Grok API request error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error with Grok API: {e}")
        
        return None
    
    def _try_openai(self, question: str) -> Optional[str]:
        """Try to get response from OpenAI API"""
        if not self.openai_client:
            logger.debug("OpenAI client not available")
            return None
        
        try:
            # Prepare messages with conversation history
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add recent conversation history
            for entry in self.conversation_history[-5:]:  # Last 5 exchanges
                messages.append({"role": "user", "content": entry["question"]})
                messages.append({"role": "assistant", "content": entry["response"]})
            
            # Add current question
            messages.append({"role": "user", "content": question})
            
            logger.info("Sending request to OpenAI API")
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=messages,
                max_tokens=150,
                temperature=0.7
            )
            
            if response.choices and len(response.choices) > 0:
                answer = response.choices[0].message.content.strip()
                logger.info("Received response from OpenAI API")
                return answer
            else:
                logger.warning("No choices in OpenAI API response")
            
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
        
        return None
    
    def _clean_response(self, response: str) -> str:
        """Clean AI response for speech synthesis"""
        if not response:
            return ""
        
        # Remove markdown and special formatting
        import re
        
        # Remove code blocks
        response = re.sub(r'```.*?```', '', response, flags=re.DOTALL)
        response = re.sub(r'`[^`]*`', '', response)
        
        # Remove markdown formatting
        response = re.sub(r'\*\*([^*]*)\*\*', r'\1', response)  # Bold
        response = re.sub(r'\*([^*]*)\*', r'\1', response)      # Italic
        response = re.sub(r'__([^_]*)__', r'\1', response)      # Bold
        response = re.sub(r'_([^_]*)_', r'\1', response)        # Italic
        
        # Remove links but keep text
        response = re.sub(r'\[([^\]]*)\]\([^)]*\)', r'\1', response)
        
        # Clean up whitespace
        response = re.sub(r'\s+', ' ', response).strip()
        
        # Ensure proper sentence ending
        if response and not response.endswith(('.', '!', '?')):
            response += '.'
        
        return response
    
    def _update_conversation_history(self, question: str, response: str):
        """Update conversation history"""
        self.conversation_history.append({
            "question": question,
            "response": response,
            "timestamp": time.time()
        })
        
        # Keep only recent history
        if len(self.conversation_history) > self.max_history_length:
            self.conversation_history = self.conversation_history[-self.max_history_length:]
    
    def get_conversation_summary(self) -> str:
        """Get a summary of recent conversation"""
        if not self.conversation_history:
            return "No recent conversation."
        
        recent_topics = []
        for entry in self.conversation_history[-3:]:
            question = entry["question"][:50] + "..." if len(entry["question"]) > 50 else entry["question"]
            recent_topics.append(question)
        
        return f"Recent topics: {', '.join(recent_topics)}"
    
    def clear_conversation_history(self):
        """Clear conversation history"""
        self.conversation_history = []
        logger.info("Conversation history cleared")
    
    def set_personality(self, personality_prompt: str):
        """
        Set a custom personality for the assistant
        
        Args:
            personality_prompt: Custom system prompt for personality
        """
        self.system_prompt = personality_prompt
        logger.info("Assistant personality updated")
    
    def get_available_models(self) -> Dict[str, bool]:
        """
        Check which AI models are available
        
        Returns:
            Dictionary of model availability
        """
        availability = {
            'grok': bool(self.grok_headers),
            'openai': bool(self.openai_client)
        }
        
        # Test actual connectivity
        if availability['grok']:
            try:
                test_response = self._try_grok("Hello")
                availability['grok_working'] = bool(test_response)
            except:
                availability['grok_working'] = False
        
        if availability['openai']:
            try:
                test_response = self._try_openai("Hello")
                availability['openai_working'] = bool(test_response)
            except:
                availability['openai_working'] = False
        
        return availability
    
    def generate_contextual_response(self, intent: str, entities: Dict[str, Any]) -> str:
        """
        Generate contextual response based on intent and entities
        
        Args:
            intent: Detected intent (e.g., 'weather', 'time', 'calculation')
            entities: Extracted entities from user input
            
        Returns:
            Contextual response
        """
        context_prompts = {
            'weather': "The user is asking about weather. Provide helpful weather information.",
            'time': "The user is asking about time. Provide current time information.",
            'calculation': "The user wants to perform a calculation. Help with math.",
            'general': "This is a general question. Provide helpful information."
        }
        
        prompt = context_prompts.get(intent, context_prompts['general'])
        
        # Create contextual question
        contextual_question = f"{prompt} User input: {entities.get('original_text', '')}"
        
        return self.ask_question(contextual_question)

# Import time for timestamp
import time
