2025-06-17 11:56:00,726 - comtypes.client._code_cache - INFO - Could not import comtypes.gen, trying to create it.
2025-06-17 11:56:00,726 - comtypes.client._code_cache - INFO - Created comtypes.gen directory: 'C:\Users\<USER>\Desktop\Voice assistant\.venv\Lib\site-packages\comtypes\gen'
2025-06-17 11:56:00,727 - comtypes.client._code_cache - INFO - Writing __init__.py file: 'C:\Users\<USER>\Desktop\Voice assistant\.venv\Lib\site-packages\comtypes\gen\__init__.py'
2025-06-17 11:56:00,737 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\Deskt<PERSON>\Voice assistant\.venv\Lib\site-packages\comtypes\gen'
2025-06-17 11:56:00,767 - comtypes.client._generate - INFO - Could not import comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4: No module named 'comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4'
2025-06-17 11:56:00,771 - comtypes.client._generate - INFO - # Generating comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4
2025-06-17 11:56:00,858 - comtypes.client._generate - INFO - # Generating comtypes.gen.SpeechLib
2025-06-17 11:56:00,865 - comtypes.client._generate - INFO - Could not import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0: No module named 'comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0'
2025-06-17 11:56:00,866 - comtypes.client._generate - INFO - # Generating comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0
2025-06-17 11:56:00,874 - comtypes.client._generate - INFO - # Generating comtypes.gen.stdole
2025-06-17 11:56:02,636 - core.responder - INFO - Using voice: Microsoft Zira Desktop - English (United States)
2025-06-17 11:56:02,636 - core.responder - INFO - Voice configured - Rate: 200, Volume: 0.9
2025-06-17 11:56:02,636 - core.responder - INFO - TTS engine initialized successfully
2025-06-17 11:56:02,638 - core.responder - INFO - Voice responder cleaned up
2025-06-17 11:56:05,441 - core.ai - INFO - OpenAI client initialized
2025-06-17 11:56:05,442 - core.ai - INFO - Sending request to Grok API
2025-06-17 11:56:06,184 - core.ai - ERROR - Grok API error: 400 - {"code":"Client specified an invalid argument","error":"Incorrect API key provided: yo***re. You can obtain an API key from https://console.x.ai."}
2025-06-17 11:56:06,185 - core.ai - INFO - Sending request to OpenAI API
2025-06-17 11:56:46,638 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 520 "
2025-06-17 11:56:46,641 - openai._base_client - INFO - Retrying request to /chat/completions in 0.405393 seconds
2025-06-17 11:56:47,505 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-06-17 11:56:47,508 - core.ai - ERROR - OpenAI API error: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-06-17 11:57:52,290 - __main__ - INFO - Initializing voice assistant components...
2025-06-17 11:57:52,335 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\Desktop\\Voice assistant\\.venv\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-06-17 11:57:52,336 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\Desktop\Voice assistant\.venv\Lib\site-packages\comtypes\gen'
2025-06-17 11:57:52,472 - core.responder - INFO - Using voice: Microsoft Zira Desktop - English (United States)
2025-06-17 11:57:52,472 - core.responder - INFO - Voice configured - Rate: 200, Volume: 0.9
2025-06-17 11:57:52,473 - core.responder - INFO - TTS engine initialized successfully
2025-06-17 11:57:52,918 - core.ai - INFO - OpenAI client initialized
2025-06-17 11:57:52,923 - __main__ - INFO - All available components initialized successfully
2025-06-17 11:57:52,924 - core.responder - INFO - Speaking: Hello! Nova is ready. I can help with system information, time, and basic tasks....
2025-06-17 11:57:59,191 - core.responder - INFO - Speech completed
2025-06-17 11:57:59,192 - __main__ - INFO - Nova Voice Assistant initialized
2025-06-17 11:58:15,161 - __main__ - INFO - Processing command: what time is it
2025-06-17 11:58:15,161 - core.responder - INFO - Speaking: It's currently 11:58 AM....
2025-06-17 11:58:17,595 - core.responder - INFO - Speech completed
2025-06-17 11:58:23,971 - __main__ - INFO - Processing command: hello
2025-06-17 11:58:23,972 - core.ai - INFO - Sending request to Grok API
2025-06-17 11:58:24,662 - core.ai - ERROR - Grok API error: 400 - {"code":"Client specified an invalid argument","error":"Incorrect API key provided: yo***re. You can obtain an API key from https://console.x.ai."}
2025-06-17 11:58:24,664 - core.ai - INFO - Sending request to OpenAI API
2025-06-17 11:58:25,558 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-06-17 11:58:25,561 - core.ai - ERROR - OpenAI API error: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-06-17 11:58:25,563 - core.responder - INFO - Speaking: I'd love to answer that, but my A I capabilities need valid A P I keys. Please check your .env file ...
2025-06-17 11:58:33,047 - core.responder - INFO - Speech completed
2025-06-17 11:58:39,986 - __main__ - INFO - Processing command: battery status
2025-06-17 11:58:39,988 - core.responder - INFO - Speaking: No battery detected (desktop computer)....
2025-06-17 11:58:43,114 - core.responder - INFO - Speech completed
2025-06-17 11:58:49,082 - __main__ - INFO - Processing command: system info
2025-06-17 11:58:50,127 - core.responder - INFO - Speaking: System status: C P U usage is 21.0 percent, memory usage is 65.9 percent....
2025-06-17 11:58:57,119 - core.responder - INFO - Speech completed
2025-06-17 11:58:57,122 - core.responder - INFO - Speaking: Goodbye! Nova is shutting down....
2025-06-17 11:59:00,047 - core.responder - INFO - Speech completed
2025-06-17 11:59:00,048 - core.responder - INFO - Voice responder cleaned up
