"""
AI Voice Assistant - Working Version
This version works with currently installed dependencies
"""

import os
import sys
import time
import logging
from datetime import datetime

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Core imports that are working
from core.responder import VoiceResponder
from core.actions import SystemActions
from core.ai import AIAssistant
from utils.config import config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/assistant.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class WorkingVoiceAssistant:
    """Working Voice Assistant with available components"""
    
    def __init__(self):
        """Initialize the voice assistant"""
        self.assistant_name = config.voice_settings['assistant_name']
        self.is_running = False
        
        # Initialize working components
        self.responder = None
        self.ai_assistant = None
        self.system_actions = None
        
        # Initialize components
        self._initialize_components()
        
        logger.info(f"{self.assistant_name} Voice Assistant initialized")
    
    def _initialize_components(self):
        """Initialize available assistant components"""
        try:
            logger.info("Initializing voice assistant components...")
            
            # Initialize responder (text-to-speech)
            self.responder = VoiceResponder()
            logger.info("✅ Text-to-speech initialized")
            
            # Initialize AI assistant
            self.ai_assistant = AIAssistant()
            logger.info("✅ AI assistant initialized")
            
            # Initialize system actions
            self.system_actions = SystemActions()
            logger.info("✅ System actions initialized")
            
            logger.info("All available components initialized successfully")
            
            # Welcome message
            if self.responder:
                welcome_msg = f"Hello! {self.assistant_name} is ready. I can help with system information, time, and basic tasks."
                self.responder.speak(welcome_msg)
            
        except Exception as e:
            logger.error(f"Error initializing components: {e}")
            print(f"Error initializing components: {e}")
    
    def process_text_command(self, command: str) -> str:
        """
        Process a text command and return response
        
        Args:
            command: User command text
            
        Returns:
            Response text
        """
        command_lower = command.lower().strip()
        
        try:
            logger.info(f"Processing command: {command}")
            
            # Time/date commands
            if any(word in command_lower for word in ['time', 'what time']):
                return self._handle_time()
            
            elif any(word in command_lower for word in ['date', 'what date', 'today']):
                return self._handle_date()
            
            # System information commands
            elif any(word in command_lower for word in ['battery', 'power']):
                return self._handle_battery()
            
            elif any(word in command_lower for word in ['cpu', 'processor', 'performance']):
                return self._handle_cpu()
            
            elif any(word in command_lower for word in ['memory', 'ram']):
                return self._handle_memory()
            
            elif any(word in command_lower for word in ['system', 'computer']):
                return self._handle_system_info()
            
            # Application launch commands
            elif any(word in command_lower for word in ['open', 'launch', 'start']):
                return self._handle_app_launch(command)
            
            # Screenshot command
            elif 'screenshot' in command_lower:
                return self._handle_screenshot()
            
            # Help command
            elif any(word in command_lower for word in ['help', 'what can you do']):
                return self._handle_help()
            
            # AI questions (if API keys are configured)
            else:
                return self._handle_ai_question(command)
                
        except Exception as e:
            logger.error(f"Error processing command '{command}': {e}")
            return "I'm sorry, I encountered an error processing that request."
    
    def _handle_time(self) -> str:
        """Handle time requests"""
        try:
            now = datetime.now()
            current_time = now.strftime("%I:%M %p")
            return f"It's currently {current_time}."
        except Exception as e:
            logger.error(f"Error getting time: {e}")
            return "I couldn't get the current time."
    
    def _handle_date(self) -> str:
        """Handle date requests"""
        try:
            now = datetime.now()
            current_date = now.strftime("%A, %B %d, %Y")
            return f"Today is {current_date}."
        except Exception as e:
            logger.error(f"Error getting date: {e}")
            return "I couldn't get the current date."
    
    def _handle_battery(self) -> str:
        """Handle battery status requests"""
        try:
            battery_info = self.system_actions.get_battery_status()
            return battery_info['message']
        except Exception as e:
            logger.error(f"Error getting battery status: {e}")
            return "I couldn't get the battery status."
    
    def _handle_cpu(self) -> str:
        """Handle CPU usage requests"""
        try:
            system_info = self.system_actions.get_system_info()
            if 'error' in system_info:
                return "I couldn't retrieve CPU information."
            
            cpu_usage = system_info['cpu']['usage_percent']
            return f"CPU usage is currently {cpu_usage:.1f} percent."
        except Exception as e:
            logger.error(f"Error getting CPU info: {e}")
            return "I couldn't get the CPU information."
    
    def _handle_memory(self) -> str:
        """Handle memory usage requests"""
        try:
            system_info = self.system_actions.get_system_info()
            if 'error' in system_info:
                return "I couldn't retrieve memory information."
            
            memory_usage = system_info['memory']['usage_percent']
            memory_total = system_info['memory']['total_gb']
            memory_available = system_info['memory']['available_gb']
            
            return f"Memory usage is {memory_usage:.1f} percent. You have {memory_available:.1f} GB available out of {memory_total:.1f} GB total."
        except Exception as e:
            logger.error(f"Error getting memory info: {e}")
            return "I couldn't get the memory information."
    
    def _handle_system_info(self) -> str:
        """Handle general system information requests"""
        try:
            system_info = self.system_actions.get_system_info()
            if 'error' in system_info:
                return "I couldn't retrieve system information."
            
            cpu_usage = system_info['cpu']['usage_percent']
            memory_usage = system_info['memory']['usage_percent']
            
            response = f"System status: CPU usage is {cpu_usage:.1f} percent, memory usage is {memory_usage:.1f} percent"
            
            if system_info.get('battery'):
                battery_percent = system_info['battery']['percent']
                response += f", and battery is at {battery_percent} percent"
            
            return response + "."
        except Exception as e:
            logger.error(f"Error getting system info: {e}")
            return "I couldn't retrieve system information."
    
    def _handle_app_launch(self, command: str) -> str:
        """Handle application launch commands"""
        try:
            # Extract app name
            command_lower = command.lower()
            
            # Remove command words
            for word in ['open', 'launch', 'start', 'run']:
                command_lower = command_lower.replace(word, '').strip()
            
            if not command_lower:
                return "Please specify which application you'd like me to open."
            
            result = self.system_actions.launch_application(command_lower)
            
            if result['success']:
                return f"Opening {result['app_name']}."
            else:
                return result['message']
                
        except Exception as e:
            logger.error(f"Error handling app launch: {e}")
            return "I couldn't launch that application."
    
    def _handle_screenshot(self) -> str:
        """Handle screenshot command"""
        try:
            result = self.system_actions.take_screenshot()
            
            if result['success']:
                return f"Screenshot saved as {result['filename']}."
            else:
                return "I couldn't take a screenshot."
                
        except Exception as e:
            logger.error(f"Error taking screenshot: {e}")
            return "I encountered an error taking the screenshot."
    
    def _handle_help(self) -> str:
        """Handle help requests"""
        return """I can help you with:
        - Time and date: 'what time is it', 'what's the date'
        - System info: 'battery status', 'CPU usage', 'memory usage'
        - Open apps: 'open notepad', 'launch chrome'
        - Take screenshots: 'take a screenshot'
        - Answer questions if API keys are configured
        Just type your command or question!"""
    
    def _handle_ai_question(self, command: str) -> str:
        """Handle general AI questions"""
        try:
            if self.ai_assistant:
                # Check if API keys are working
                available_models = self.ai_assistant.get_available_models()
                if available_models.get('grok_working') or available_models.get('openai_working'):
                    response = self.ai_assistant.ask_question(command)
                    return response
                else:
                    return "I'd love to answer that, but my AI capabilities need valid API keys. Please check your .env file configuration."
            else:
                return "I'm sorry, my AI capabilities are not available right now."
                
        except Exception as e:
            logger.error(f"Error handling AI question: {e}")
            return "I'm having trouble processing that question right now."
    
    def interactive_mode(self):
        """Run in interactive text mode"""
        print(f"\n🤖 {self.assistant_name} Interactive Mode")
        print("=" * 50)
        print("Type your commands or questions. Type 'quit' to exit.")
        print("Examples:")
        print("  - what time is it")
        print("  - battery status")
        print("  - open notepad")
        print("  - take a screenshot")
        print("  - help")
        print("-" * 50)
        
        while True:
            try:
                user_input = input(f"\n🗣️ You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'goodbye']:
                    response = f"Goodbye! {self.assistant_name} is shutting down."
                    print(f"🤖 {self.assistant_name}: {response}")
                    if self.responder:
                        self.responder.speak(response)
                    break
                
                if user_input:
                    # Process command
                    response = self.process_text_command(user_input)
                    print(f"🤖 {self.assistant_name}: {response}")
                    
                    # Speak response
                    if self.responder:
                        self.responder.speak(response)
                
            except KeyboardInterrupt:
                print(f"\n🤖 {self.assistant_name}: Goodbye!")
                break
            except Exception as e:
                print(f"Error: {e}")
    
    def start(self):
        """Start the voice assistant"""
        try:
            print(f"\n🚀 Starting {self.assistant_name} Voice Assistant...")
            print("Note: This version uses text input since speech recognition requires additional setup.")
            
            # Show configuration status
            validation_results = config.validate_config()
            print(f"Configuration status: {validation_results}")
            
            # Start interactive mode
            self.interactive_mode()
                
        except Exception as e:
            logger.error(f"Error starting assistant: {e}")
            print(f"Error starting assistant: {e}")

def main():
    """Main entry point"""
    try:
        print(f"🤖 {config.voice_settings['assistant_name']} AI Voice Assistant")
        print("Working Version - Text Input Mode")
        print("=" * 50)
        
        # Create and start assistant
        assistant = WorkingVoiceAssistant()
        assistant.start()
        
    except KeyboardInterrupt:
        print("\nShutting down...")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"Fatal error: {e}")
    finally:
        print("Goodbye!")

if __name__ == "__main__":
    main()
