# AI Voice Assistant Configuration Template
# Copy this file to .env and fill in your actual API keys and settings

# AI API Configuration
GROK_API_KEY=your_grok_api_key_here
HUGGINGFACE_API_KEY=your_huggingface_api_key_here

# Email Configuration (Gmail)
GMAIL_EMAIL=<EMAIL>
GMAIL_APP_PASSWORD=your_gmail_app_password_here

# Email Configuration (Outlook)
OUTLOOK_EMAIL=<EMAIL>
OUTLOOK_PASSWORD=your_outlook_password_here

# Voice Assistant Settings
WAKE_WORD=hey nova
ASSISTANT_NAME=Nova
VOICE_RATE=200
VOICE_VOLUME=0.9

# System Settings
LOG_LEVEL=INFO
AUDIO_DEVICE_INDEX=-1
MICROPHONE_SENSITIVITY=0.5

# Image Generation Settings
IMAGE_SAVE_PATH=./generated_images/
DEFAULT_IMAGE_MODEL=stabilityai/stable-diffusion-2-1

# Application Paths (Windows)
CHROME_PATH=C:\Program Files\Google\Chrome\Application\chrome.exe
VSCODE_PATH=C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe
NOTEPAD_PATH=C:\Windows\System32\notepad.exe
CALCULATOR_PATH=C:\Windows\System32\calc.exe
