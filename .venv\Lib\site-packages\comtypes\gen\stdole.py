from enum import IntFlag

import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 as __wrapper_module__
from comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 import (
    IPictureDisp, IFont, Font, OLE_YPOS_HIMETRIC, GUID, typelib_path,
    OLE_COLOR, VgaColor, Monochrome, IDispatch, DISPPARAMS, dispid,
    FONTBOLD, VARIANT_BOOL, Checked, OLE_HANDLE, DISPMETHOD,
    OLE_XPOS_HIMETRIC, StdPicture, OLE_XSIZE_PIXELS, Unchecked,
    <PERSON><PERSON><PERSON><PERSON><PERSON>, Picture, <PERSON><PERSON><PERSON>ROPERTY, HRESULT, OLE_OPTEXCLUSIVE,
    CoClass, <PERSON><PERSON><PERSON>HOD, OLE_YPOS_PIXELS, FontEvents, OLE_XPOS_PIXELS,
    IEnumVARIANT, OLE_YSIZE_HIMETRIC, <PERSON><PERSON>_XSIZE_HIMETRIC,
    OLE_YPOS_CONTAINER, Library, Default, Color, BSTR, _check_version,
    OLE_XSIZE_CONTAINER, OLE_YSIZE_PIXELS, _lcid, IFontEventsDisp,
    IFontDisp, OLE_XPOS_CONTAINER, FONTUNDERSCORE,
    OLE_YSIZE_CONTAINER, OLE_ENABLEDEFAULTBOOL, IPicture, FONTITALIC,
    FONTSIZE, OLE_CANCELBOOL, FONTSTRIKETHROUGH, Gray, StdFont,
    IUnknown, EXCEPINFO
)


class OLE_TRISTATE(IntFlag):
    Unchecked = 0
    Checked = 1
    Gray = 2


class LoadPictureConstants(IntFlag):
    Default = 0
    Monochrome = 1
    VgaColor = 2
    Color = 4


__all__ = [
    'IPictureDisp', 'IFont', 'OLE_TRISTATE', 'Font',
    'OLE_YPOS_HIMETRIC', 'typelib_path', 'OLE_COLOR', 'FontEvents',
    'OLE_XPOS_PIXELS', 'VgaColor', 'Monochrome', 'OLE_YSIZE_HIMETRIC',
    'OLE_XSIZE_HIMETRIC', 'OLE_YPOS_CONTAINER', 'Library', 'Default',
    'Color', 'FONTBOLD', 'Checked', 'OLE_HANDLE',
    'OLE_XSIZE_CONTAINER', 'OLE_YSIZE_PIXELS', 'IFontEventsDisp',
    'IFontDisp', 'OLE_XPOS_HIMETRIC', 'StdPicture',
    'OLE_XSIZE_PIXELS', 'Unchecked', 'FONTNAME', 'OLE_XPOS_CONTAINER',
    'FONTUNDERSCORE', 'Picture', 'OLE_YSIZE_CONTAINER',
    'LoadPictureConstants', 'OLE_ENABLEDEFAULTBOOL', 'IPicture',
    'FONTITALIC', 'FONTSIZE', 'OLE_CANCELBOOL', 'FONTSTRIKETHROUGH',
    'OLE_OPTEXCLUSIVE', 'Gray', 'StdFont', 'OLE_YPOS_PIXELS'
]

