from enum import IntFlag

import comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 as __wrapper_module__
from comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 import (
    SPCS_DISABLED, SAFT11kHz16BitStereo, ULONG_PTR, DISPID_SGRsCommit,
    SWTDele<PERSON>, DISPID_SOTGetStorageFileName, eLEXTYPE_PRIVATE20,
    DISPID_SRAudioInput, SPDKL_CurrentUser, SVP_20,
    DISPID_SGRSTWeight, DISPID_SGRSTs_NewEnum, SVPAlert,
    DISPID_SRCEventInterests, STSF_CommonAppData, SGRSTTWildcard,
    ISpeechFileStream, ISpeechPhraseProperty, SPEI_RESERVED2,
    IServiceProvider, SSSPTRelativeToEnd, SpeechAudioFormatGUIDText,
    DISPID_SVEVoiceChange, DISPID_SRSCurrentStreamNumber,
    SAFTTrueSpeech_8kHz1BitMono, Speech_Max_Word_Length,
    DISPID_SRSNumberOfActiveRules, DISPID_SASCurrentDevicePosition,
    SP_VISEME_21, DISPID_SRCEAudioLevel, SAFT8kHz16BitStereo, SPSNoun,
    STCLocalServer, eLEXTYPE_MORPHOLOGY, DISPID_SPIStartTime,
    SVEAudioLevel, SECFNoSpecialChars, eLEXTYPE_RESERVED8, SPSUnknown,
    SVESentenceBoundary, SP_VISEME_4, ISpeechPhraseRule, SVP_0,
    SpeechCategoryRecognizers, ISpRecognizer2, SPGS_DISABLED,
    SPSFunction, DISPID_SBSWrite, SPEI_START_INPUT_STREAM,
    SPEI_PROPERTY_NUM_CHANGE, DISPID_SRIsUISupported,
    ISpeechObjectToken, eLEXTYPE_RESERVED9, SPEI_FALSE_RECOGNITION,
    SVP_5, ISpeechPhraseAlternate, SAFT32kHz8BitStereo,
    ISpeechPhraseElements, DISPID_SVDisplayUI, DISPID_SVEViseme,
    _LARGE_INTEGER, DISPID_SRRSaveToMemory, SpeechAudioProperties,
    ISpeechLexiconPronunciation, SRADefaultToActive,
    SPPS_SuppressWord, SPGS_ENABLED, DISPID_SRDisplayUI,
    DISPID_SGRsFindRule, DISPID_SPAStartElementInResult,
    DISPIDSPTSI_SelectionOffset, DISPID_SDKCreateKey,
    SpeechGrammarTagUnlimitedDictation, SAFTADPCM_11kHzStereo,
    SECFIgnoreCase, SAFT11kHz8BitMono, SLTUser, SECHighConfidence,
    SINone, IInternetSecurityManager, DISPID_SRCRetainedAudio,
    SAFTCCITT_uLaw_22kHzMono, SITooLoud, SAFTCCITT_uLaw_8kHzMono,
    WSTRING, SPEI_VISEME, ISpeechRecoResult,
    DISPID_SABufferNotifySize, ISpeechGrammarRules, SECLowConfidence,
    DISPID_SPAsCount, SGDSActiveWithAutoPause, SAFTGSM610_44kHzMono,
    SpInProcRecoContext, DISPID_SRCBookmark, SVPOver, SREAdaptation,
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet, eLEXTYPE_PRIVATE6,
    DISPID_SPIGetText, ISpeechAudioFormat, SREPropertyNumChange,
    SpPhoneticAlphabetConverter, SPAR_Medium, SDTDisplayText,
    DISPIDSPTSI_SelectionLength, ISequentialStream,
    SPRST_INACTIVE_WITH_PURGE, SPRS_ACTIVE_WITH_AUTO_PAUSE,
    SPPHRASEREPLACEMENT, SAFT32kHz16BitMono, SGRSTTEpsilon,
    DISPID_SWFEChannels, SRSEIsSpeaking, STCInprocServer,
    SREInterference, DISPID_SGRSAddRuleTransition, SPBO_TIME_UNITS,
    SRTAutopause, DISPID_SLPsItem, SPSLMA, SPVOICESTATUS,
    SPEI_START_SR_STREAM, SVP_6, DISPID_SVResume,
    DISPID_SRGCmdLoadFromMemory, DISPID_SGRSTsItem, VARIANT,
    SpMMAudioEnum, DISPID_SPPValue, DISPID_SFSClose,
    ISpeechVoiceStatus, DISPID_SLWs_NewEnum, SVSFIsNotXML,
    STCInprocHandler, DISPID_SAStatus, DISPID_SVEStreamStart,
    DISPIDSPTSI_ActiveOffset, SITooSlow, DISPID_SPERetainedSizeBytes,
    SPPS_Unknown, ISpRecoCategory, DISPID_SPAs_NewEnum,
    SWPUnknownWordPronounceable, DISPID_SMSGetData, LONG_PTR, IStream,
    eLEXTYPE_USER, SPWORD, STCAll, ISpEventSink,
    DISPID_SRGIsPronounceable, SpFileStream, SpeechCategoryAudioOut,
    DISPID_SRProfile, DISPID_SRCERecognitionForOtherContext,
    SPEI_PHONEME, DISPID_SRRDiscardResultInfo, DISPID_SPRulesCount,
    ISpeechPhraseReplacement, DISPID_SPIEngineId,
    DISPID_SPRFirstElement, STSF_FlagCreate, ISpPhrase,
    DISPID_SGRSTPropertyValue, SRESoundStart, SPEVENTSOURCEINFO,
    SAFTCCITT_ALaw_44kHzMono, SP_VISEME_0, SP_VISEME_17, SRSActive,
    SVP_14, ISpeechObjectTokenCategory, SDKLCurrentConfig,
    DISPID_SGRId, DISPID_SPIProperties, STSF_LocalAppData,
    SDA_Two_Trailing_Spaces, SSSPTRelativeToStart, SPEI_PHRASE_START,
    SP_VISEME_16, SAFT24kHz8BitStereo, DISPID_SGRAttributes,
    SpeechPropertyResponseSpeed, IUnknown, SPRST_ACTIVE_ALWAYS,
    DISPID_SPRuleEngineConfidence, SVSFNLPSpeakPunc,
    DISPID_SRGSetTextSelection, ISpeechAudioStatus, SINoSignal,
    DISPID_SRCEAdaptation, SPVPRI_NORMAL,
    DISPID_SLAddPronunciationByPhoneIds, SAFT16kHz8BitStereo,
    DISPID_SRCResume, SPSMF_UPS, DISPID_SRGetPropertyString,
    DISPID_SPIRetainedSizeBytes, DISPID_SPPFirstElement,
    eLEXTYPE_PRIVATE2, SSTTWildcard, SPPS_Verb, SDTAlternates,
    DISPID_SVSpeak, SASRun, SPBINARYGRAMMAR, ISpRecoGrammar2,
    SREStateChange, SPEI_MAX_TTS, SAFT44kHz8BitStereo, ISpeechLexicon,
    SGDSActive, DISPID_SPPs_NewEnum, SRAONone, SpCustomStream,
    DISPID_SOTsItem, DISPID_SRCEStartStream, SP_VISEME_15,
    WAVEFORMATEX, SPWORDLIST, Speech_StreamPos_Asap,
    eLEXTYPE_PRIVATE11, SPPHRASEPROPERTY, SRTStandard,
    DISPID_SGRs_NewEnum, tagSPPROPERTYINFO, SPEI_RESERVED1,
    SREPropertyStringChange, DISPID_SPAsItem, DISPID_SRRPhraseInfo,
    SAFTCCITT_ALaw_22kHzStereo, DISPID_SPEsCount, DISPID_SOTDataKey,
    DISPID_SRCSetAdaptationData, SpeechCategoryPhoneConverters,
    DISPID_SRCPause, SPRULE, SVEAllEvents, DISPID_SWFEBlockAlign,
    DISPID_SWFEFormatTag, ISpObjectTokenCategory, SPEI_SR_PRIVATE,
    ISpStreamFormatConverter, DISPID_SAFGuid, DISPID_SPISaveToMemory,
    DISPID_SPPId, SpeechPropertyHighConfidenceThreshold,
    DISPID_SVSPhonemeId, DISPID_SVEWord, DISPID_SVPriority,
    ISpeechLexiconWords, GUID, SPFM_CREATE,
    DISPID_SRCRetainedAudioFormat,
    SpeechPropertyNormalConfidenceThreshold, SVP_11,
    SDKLDefaultLocation, ISpRecoContext2, DISPID_SOTCGetDataKey,
    DISPID_SVPause, DISPID_SRRSetTextFeedback, DISPID_SOTRemove,
    SSFMCreateForWrite, SpeechPropertyLowConfidenceThreshold,
    DISPID_SRAudioInputStream, DISPID_SRCCreateResultFromMemory,
    DISPMETHOD, DISPID_SVWaitUntilDone, DISPID_SRState,
    ISpeechBaseStream, SPEI_SR_AUDIO_LEVEL, SAFTADPCM_11kHzMono,
    DISPID_SLPsCount, DISPID_SRIsShared, SAFT16kHz8BitMono,
    SPINTERFERENCE_LATENCY_WARNING, SRSActiveAlways,
    SpeechPropertyComplexResponseSpeed, SPCT_SLEEP,
    DISPID_SAEventHandle, SPSHORTCUTPAIR, SPVPRI_OVER,
    SPSSuppressWord, DISPID_SRStatus, SPAUDIOBUFFERINFO,
    SPEI_RESERVED6, DISPID_SRCERequestUI, SAFT8kHz16BitMono,
    SSTTDictation, SPWP_UNKNOWN_WORD_PRONOUNCEABLE,
    DISPID_SVSVisemeId, DISPID_SLWPronunciations, DISPID_SGRsItem,
    Speech_Max_Pron_Length, ISpRecognizer3,
    SpTextSelectionInformation, DISPID_SRRAudio,
    DISPID_SPPNumberOfElements, SPEI_TTS_BOOKMARK, SP_VISEME_12,
    SPWT_LEXICAL, SPFM_OPEN_READONLY, DISPID_SDKEnumKeys, SSFMCreate,
    SPCT_COMMAND, SP_VISEME_11, SPINTERFERENCE_TOOQUIET,
    SAFT48kHz8BitMono, SAFT12kHz16BitStereo, SAFT24kHz16BitMono,
    IEnumString, SPEI_MAX_SR, SPEI_WORD_BOUNDARY,
    SPEI_SENTENCE_BOUNDARY, ISpeechMMSysAudio, SVSFIsFilename,
    SPFM_NUM_MODES, SpeechCategoryAudioIn, ISpeechPhraseAlternates,
    DISPID_SRCEHypothesis, DISPID_SRAllowVoiceFormatMatchingOnNextSet,
    eWORDTYPE_DELETED, DISPID_SOTIsUISupported, SPGS_EXCLUSIVE,
    SpShortcut, DISPID_SPIAudioStreamPosition, SREStreamStart,
    DISPID_SVAudioOutputStream, DISPID_SPIAudioSizeBytes, SpLexicon,
    SGRSTTWord, DISPID_SRAllowAudioInputFormatChangesOnNextSet,
    ISpDataKey, ISpeechGrammarRuleStateTransition,
    SPINTERFERENCE_TOOSLOW, SVSFPurgeBeforeSpeak, ISpProperties,
    SPWF_SRENGINE, SVSFUnusedFlags, SPINTERFERENCE_NOSIGNAL,
    ISpeechLexiconWord, DISPID_SLPType, DISPID_SAFSetWaveFormatEx,
    SAFTGSM610_8kHzMono, SPINTERFERENCE_TOOFAST,
    DISPID_SVSInputSentencePosition, DISPID_SPRuleConfidence, SPSVerb,
    SVSFParseAutodetect, DISPID_SGRSTPropertyId,
    tagSPTEXTSELECTIONINFO, SPINTERFERENCE_NONE, DISPID_SLPs_NewEnum,
    ISpeechRecoResult2, DISPID_SABufferInfo,
    _ISpeechRecoContextEvents, SGDisplay, SpeechPropertyAdaptationOn,
    DISPID_SGRSTRule, DISPID_SRRTOffsetFromStart, eLEXTYPE_PRIVATE17,
    SPTEXTSELECTIONINFO, SDA_Consume_Leading_Spaces,
    SpeechGrammarTagDictation, DISPID_SMSSetData, SPSNotOverriden,
    SAFTCCITT_uLaw_44kHzStereo, ISpeechWaveFormatEx, SVP_4,
    DISPID_SPRDisplayAttributes, DISPID_SRGCmdLoadFromResource,
    DISPID_SDKOpenKey, BSTR, SGLexicalNoSpecialChars,
    SpeechGrammarTagWildcard, STCRemoteServer,
    DISPID_SWFESamplesPerSec, DISPID_SPCLangId, SpObjectTokenCategory,
    DISPID_SPPChildren, DISPID_SRSetPropertyNumber, UINT_PTR,
    SPLO_STATIC, ISpeechVoice, DISPID_SPRs_NewEnum, SPEVENT,
    SPSEMANTICERRORINFO, SINoise, SAFTADPCM_8kHzMono,
    DISPID_SRGCommit, SPRECORESULTTIMES, SPPS_RESERVED1,
    SAFTCCITT_ALaw_8kHzStereo, DISPID_SPRuleId, ISpEventSource,
    SPPS_Noun, SRAInterpreter, eLEXTYPE_PRIVATE15, SPRST_NUM_STATES,
    DISPID_SGRSTransitions, SPPHRASEELEMENT, SpMemoryStream, SVP_8,
    DISPID_SVIsUISupported, DISPID_SRSClsidEngine, SVF_Emphasis,
    eLEXTYPE_LETTERTOSOUND, helpstring, DISPID_SGRsDynamic,
    SFTSREngine, SDTPronunciation, SAFT48kHz16BitMono,
    SVEEndInputStream, SDTLexicalForm, SAFTNoAssignedFormat,
    DISPID_SLPSymbolic, SPPS_NotOverriden, DISPID_SRGetFormat,
    SGDSActiveUserDelimited, DISPID_SRCVoice, SVP_9,
    DISPID_SPEPronunciation, SRADynamic, SAFT24kHz8BitMono,
    SpeechMicTraining, SAFTCCITT_ALaw_22kHzMono, DISPID_SGRSRule,
    SDTReplacement, ISpeechPhraseInfoBuilder, SPAS_RUN,
    DISPID_SRSCurrentStreamPosition, SPEI_SR_BOOKMARK,
    DISPID_SPRNumberOfElements, DISPID_SRGRecoContext,
    SPWORDPRONUNCIATIONLIST, SGSDisabled, DISPID_SVEBookmark,
    SAFT22kHz8BitMono, SPEI_END_SR_STREAM, ISpeechGrammarRule,
    eWORDTYPE_ADDED, DISPID_SAVolume, DISPID_SPRText, SRARoot,
    DISPID_SGRSTText, SPSHT_OTHER, SpeechRecoProfileProperties,
    SP_VISEME_3, SPEI_VOICE_CHANGE, DISPID_SVGetAudioInputs,
    eLEXTYPE_PRIVATE16, DISPID_SPRsItem, SPCT_DICTATION,
    eLEXTYPE_USER_SHORTCUT, DISPID_SABIEventBias, SPPS_Interjection,
    DISPID_SVEAudioLevel, SVSFIsXML, DISPID_SRRGetXMLErrorInfo,
    DISPID_SRGetPropertyNumber, SAFTADPCM_44kHzMono, DISPID_SBSRead,
    DISPID_SVSCurrentStreamNumber, SPWF_INPUT, SAFT48kHz8BitStereo,
    DISPID_SPAPhraseInfo, DISPID_SOTSetId, _FILETIME,
    DISPID_SABIMinNotification, DISPID_SPCIdToPhone,
    DISPID_SVSpeakCompleteEvent, SVP_18, DISPID_SPEAudioSizeTime,
    DISPID_SASState, DISPID_SPPBRestorePhraseFromMemory,
    DISPID_SLWsCount, SPSMF_SRGS_SEMANTICINTERPRETATION_W3C,
    SpeechTokenKeyFiles, DISPIDSPTSI_ActiveLength, SRSInactive,
    SAFT12kHz8BitStereo, DISPID_SRGId, ISpPhoneticAlphabetConverter,
    SPEI_RECOGNITION, SPCT_SUB_COMMAND, SP_VISEME_10, SPSModifier,
    SAFTADPCM_44kHzStereo, DISPID_SPCPhoneToId, SpStream, SVEViseme,
    DISPID_SRCEPhraseStart, SpeechVoiceCategoryTTSRate,
    SpeechTokenIdUserLexicon, SPSInterjection, DISPID_SLPPartOfSpeech,
    ISpeechGrammarRuleState, DISPID_SRRecognizer, ISpeechPhraseInfo,
    SVEVoiceChange, SSFMOpenForRead, ISpStream,
    DISPID_SPANumberOfElementsInResult, SVP_12, SAFT22kHz16BitMono,
    DISPID_SABIBufferSize, SPAS_STOP,
    SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE, eLEXTYPE_PRIVATE12,
    DISPID_SPIGetDisplayAttributes, SAFTADPCM_22kHzMono,
    SPPS_Modifier, DISPID_SVSLastResult, SVEWordBoundary,
    ISpStreamFormat, SITooFast, SPLO_DYNAMIC, SREHypothesis,
    DISPID_SRCERecognizerStateChange, DISPID_SOTCreateInstance,
    SASPause, SAFTNonStandardFormat, SRTSMLTimeout,
    DISPID_SVEStreamEnd, DISPID_SPPEngineConfidence, wireHWND,
    SPPHRASERULE, SAFT32kHz16BitStereo, eLEXTYPE_VENDORLEXICON,
    DISPID_SRRTLength, SPSMF_SRGS_SEMANTICINTERPRETATION_MS,
    DISPID_SGRsCount, ISpeechRecognizer, DISPID_SGRsAdd,
    ISpGrammarBuilder, SVP_2, SVEBookmark, SPAR_High,
    DISPID_SVGetAudioOutputs, DISPID_SBSSeek, SP_VISEME_2,
    eLEXTYPE_APP, eLEXTYPE_RESERVED10, SpeechPropertyResourceUsage,
    SRAORetainAudio, DISPID_SPEDisplayText, SDTAudio,
    DISPID_SPRuleParent, SPEI_INTERFERENCE, eLEXTYPE_PRIVATE7,
    SPAR_Low, DISPID_SREmulateRecognition, SBONone,
    DISPID_SPERetainedStreamOffset, SRERecognition, SRCS_Disabled,
    DISPID_SPRuleNumberOfElements, eLEXTYPE_PRIVATE13,
    eLEXTYPE_PRIVATE19, SPPS_Function, SPBO_AHEAD, Library,
    DISPID_SWFEExtraData, SAFT8kHz8BitMono, SLODynamic,
    DISPID_SASNonBlockingIO, eLEXTYPE_PRIVATE14, SLOStatic,
    ISpPhoneConverter, DISPID_SVRate, SPINTERFERENCE_TOOLOUD,
    DISPID_SFSOpen, SSTTTextBuffer, DISPID_SRRSpeakAudio,
    DISPID_SOTCategory, DISPID_SRGCmdSetRuleState,
    SpStreamFormatConverter, SPEI_SOUND_END,
    DISPID_SVSInputSentenceLength, SPRECOCONTEXTSTATUS,
    SpUnCompressedLexicon, SPEI_RECO_STATE_CHANGE,
    DISPID_SVSpeakStream, SP_VISEME_13, typelib_path,
    eLEXTYPE_PRIVATE1, SPEI_ADAPTATION,
    ISpeechGrammarRuleStateTransitions, DISPID_SVSLastBookmarkId,
    SECFEmulateResult, DISPID_SPRsCount, SDA_One_Trailing_Space,
    DISPID_SRCVoicePurgeEvent, SGRSTTTextBuffer, SPRST_INACTIVE,
    DISPID_SWFEBitsPerSample, ISpeechObjectTokens, SPSERIALIZEDRESULT,
    STSF_AppData, ISpNotifyTranslator, _ISpeechVoiceEvents,
    SAFT24kHz16BitStereo, DISPID_SVGetVoices, SVP_16, DISPID_SOTId,
    SPRS_ACTIVE, DISPID_SPEs_NewEnum, ISpXMLRecoResult,
    SpCompressedLexicon, DISPID_SPIGrammarId, SPEI_TTS_PRIVATE,
    DISPID_SAFGetWaveFormatEx, SAFT12kHz16BitMono, SPEI_REQUEST_UI,
    SpeechTokenKeyAttributes, DISPID_SRRGetXMLResult, SPBO_NONE,
    DISPID_SRCEEnginePrivate, eLEXTYPE_PRIVATE10, SVSFlagsAsync,
    SDTAll, DISPID_SGRAddState, ISpeechAudio, SPWT_DISPLAY,
    SPSHORTCUTPAIRLIST, DISPID_SVESentenceBoundary,
    DISPID_SRCEPropertyStringChange, DISPID_SPRuleFirstElement,
    SREBookmark, DISPID_SOTGetAttribute, SPWORDPRONUNCIATION,
    ISpeechPhraseProperties, SVP_15, DISPID_SPEAudioSizeBytes,
    ISpLexicon, SpNullPhoneConverter, SAFT48kHz16BitStereo,
    SpeechTokenValueCLSID, ISpNotifySource, DISPID_SAFType, SVP_17,
    IEnumSpObjectTokens, DISPID_SRGCmdLoadFromObject, DISPID_SGRName,
    SGDSInactive, SGSExclusive, DISPID_SPEDisplayAttributes,
    ISpObjectToken, DISPID_SVSyncronousSpeakTimeout,
    DISPID_SPILanguageId, DISPID_SDKGetStringValue,
    DISPID_SRCEBookmark, SREPhraseStart, DISPID_SPACommit,
    DISPID_SOTDisplayUI, SAFT16kHz16BitStereo, SAFTADPCM_22kHzStereo,
    SGPronounciation, SPPS_LMA, SpMMAudioIn,
    DISPID_SRGDictationUnload, SVEPrivate, SRTExtendableParse,
    ISpeechAudioBufferInfo, DISPID_SRGSetWordSequenceData,
    SP_VISEME_20, SAFTGSM610_22kHzMono, HRESULT, DISPID_SPRuleName,
    DISPID_SRGReset, SGLexical, SpPhraseInfoBuilder, SITooQuiet,
    ISpeechRecoGrammar, ISpeechXMLRecoResult, SDTRule,
    DISPID_SPIEnginePrivateData, DISPID_SRCCmdMaxAlternates,
    DISPID_SDKDeleteKey, DISPID_SVSInputWordPosition,
    DISPID_SVSLastStreamNumberQueued, SVP_21, SAFTADPCM_8kHzStereo,
    DISPID_SRGState, SPWT_PRONUNCIATION, ISpMMSysAudio,
    ISpeechDataKey, DISPID_SRCESoundStart, DISPID_SOTCId,
    ISpPhraseAlt, DISPID_SLGetGenerationChange, SPSERIALIZEDPHRASE,
    _lcid, ISpeechTextSelectionInformation, ISpeechPhoneConverter,
    SPSMF_SAPI_PROPERTIES, ISpVoice, SpVoice,
    DISPID_SOTMatchesAttributes, DISPID_SRCreateRecoContext,
    SPWP_KNOWN_WORD_PRONOUNCEABLE, DISPID_SVStatus,
    DISPID_SOTCDefault, DISPID_SRCERecognition,
    __MIDL___MIDL_itf_sapi_0000_0020_0001, DISPID_SVSRunningState,
    SPEI_MIN_SR, DISPID_SLWWord, SVSFParseSapi, DISPID_SRRRecoContext,
    SPEI_SOUND_START, SDTProperty, SpeechVoiceSkipTypeSentence,
    SpeechDictationTopicSpelling, DISPID_SRCRequestedUIType,
    DISPID_SDKEnumValues, SAFTCCITT_ALaw_44kHzStereo, dispid,
    DISPID_SRCCreateGrammar, ISpShortcut, DISPID_SLWType, SWTAdded,
    SPAS_PAUSE, SPEI_END_INPUT_STREAM, SFTInput, SRESoundEnd,
    SSFMOpenReadWrite, DISPID_SVVoice, DISPID_SVAlertBoundary,
    DISPID_SVGetProfiles, SVSFNLPMask, DISPID_SMSAMMHandle, CoClass,
    SLTApp, DISPID_SOTsCount, SPFM_CREATE_ALWAYS, ISpeechPhraseRules,
    SGRSTTRule, SAFTCCITT_ALaw_11kHzMono, eLEXTYPE_PRIVATE18,
    ISpeechLexiconPronunciations, DISPID_SRSSupportedLanguages,
    DISPID_SPRuleChildren, DISPID_SMSADeviceId,
    ISpeechRecognizerStatus, DISPID_SPELexicalForm,
    SpeechCategoryAppLexicons, DISPID_SOTCEnumerateTokens,
    SPINTERFERENCE_LATENCY_TRUNCATE_END, ISpeechPhraseReplacements,
    SECNormalConfidence, ISpSerializeState, SRERecoOtherContext,
    SVF_Stressed, DISPID_SVSkip, _check_version, SRAExport,
    SAFT11kHz8BitStereo, SP_VISEME_5, ISpRecoContext,
    ISpeechCustomStream, SAFT44kHz16BitMono, SPAUDIOSTATUS,
    DISPID_SRRTimes, SREAudioLevel, eLEXTYPE_RESERVED7,
    DISPID_SPIRule, SPRS_INACTIVE, DISPID_SVSLastBookmark,
    SP_VISEME_7, DISPID_SLPLangId, DISPID_SGRSTType, SP_VISEME_8,
    SPAS_CLOSED, SAFTCCITT_uLaw_22kHzStereo,
    DISPID_SRCEFalseRecognition, SVP_19, SPRECOGNIZERSTATUS,
    SPEI_SR_RETAINEDAUDIO, SPAR_Unknown, SVP_3, SRCS_Enabled,
    SREPrivate, DISPID_SRGCmdSetRuleIdState, DISPID_SRRAudioFormat,
    SAFTGSM610_11kHzMono, SpeechTokenKeyUI,
    DISPID_SPERequiredConfidence, SDA_No_Trailing_Space,
    ISpeechRecoContext, SpPhoneConverter, SPEI_RESERVED5,
    SpeechAllElements, DISPID_SASFreeBufferSpace, SPPS_RESERVED2,
    SREStreamEnd, SPDKL_LocalMachine, DISPID_SRCRecognizer,
    DISPID_SOTGetDescription, DISPID_SDKDeleteValue, SPSHT_Unknown,
    DISPID_SCSBaseStream, SRSInactiveWithPurge, ISpeechMemoryStream,
    DISPID_SPEActualConfidence, DISPID_SRGDictationSetState,
    SAFT44kHz16BitStereo, SBOPause, DISPID_SPPConfidence,
    DISPID_SVEventInterests, SPWT_LEXICAL_NO_SPECIAL_CHARS,
    DISPID_SLAddPronunciation, SpeechRegistryUserRoot,
    DISPID_SGRSTsCount, __MIDL___MIDL_itf_sapi_0000_0020_0002,
    Speech_StreamPos_RealTime, SPCS_ENABLED, SP_VISEME_9,
    SpNotifyTranslator, DISPID_SPPName, SVF_None,
    DISPID_SWFEAvgBytesPerSec, DISPID_SPIAudioSizeTime, SPPHRASE,
    SpAudioFormat, SAFTCCITT_uLaw_8kHzStereo, SPXRO_Alternates_SML,
    SSSPTRelativeToCurrentPosition, eLEXTYPE_PRIVATE3,
    SPRS_ACTIVE_USER_DELIMITED, SECFDefault, SAFT11kHz16BitMono,
    SAFT12kHz8BitMono, SPEI_UNDEFINED, SPXRO_SML, ISpResourceManager,
    SAFTCCITT_ALaw_8kHzMono, ISpRecognizer, SpeechCategoryVoices,
    SAFTCCITT_uLaw_11kHzStereo, DISPID_SVSInputWordLength,
    SpResourceManager, SPAO_RETAIN_AUDIO, SpSharedRecoContext,
    DISPID_SRGDictationLoad, DISPID_SLWLangId, SPDKL_DefaultLocation,
    SPCT_SUB_DICTATION, DISPID_SGRSTNextState, SpInprocRecognizer,
    DISPID_SRCEInterference, DISPID_SDKGetBinaryValue,
    DISPID_SVVolume, SVSFVoiceMask, SRERequestUI, SREAllEvents,
    SRAImport, SREFalseRecognition,
    SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN, DISPID_SGRClear,
    DISPID_SOTRemoveStorageFileName, Speech_Default_Weight,
    DISPID_SPIReplacements, DISPID_SPRulesItem, DISPID_SLPPhoneIds,
    DISPID_SRRTTickCount, SPEI_RECO_OTHER_CONTEXT,
    DISPID_SLGenerationId, ISpRecoGrammar,
    DISPID_SASCurrentSeekPosition, DISPID_SVEEnginePrivate,
    SpeechCategoryRecoProfiles, SECFIgnoreWidth,
    ISpeechResourceLoader, SDKLCurrentUser, SpeechAddRemoveWord,
    SpeechEngineProperties, DISPID_SPRules_NewEnum,
    DISPID_SPEAudioTimeOffset, DISPID_SRCAudioInInterferenceStatus,
    SVSFDefault, DISPID_SLRemovePronunciation,
    SpeechAudioFormatGUIDWave, DISPID_SPEAudioStreamOffset,
    __MIDL_IWinTypes_0009, SVEPhoneme,
    DISPID_SLRemovePronunciationByPhoneIds, DISPID_SPEsItem,
    ISpObjectWithToken, DISPID_SRSetPropertyString,
    DISPID_SDKSetLongValue, DISPID_SGRSAddWordTransition,
    DISPID_SLWsItem, SpObjectToken, eLEXTYPE_PRIVATE8, SPBO_PAUSE,
    ISpPhoneticAlphabetSelection, eLEXTYPE_RESERVED4,
    SPSHT_NotOverriden, VARIANT_BOOL, SP_VISEME_6, SGRSTTDictation,
    SDKLLocalMachine, DISPID_SPPsCount, SRTEmulated,
    SPEI_PROPERTY_STRING_CHANGE, DISPID_SRCEEndStream,
    SPEI_TTS_AUDIO_LEVEL, SPPROPERTYINFO, DISPID_SLGetWords,
    DISPID_SRCEPropertyNumberChange, SAFTExtendedAudioFormat,
    SpSharedRecognizer, DISPID_SPPParent, SAFTCCITT_uLaw_11kHzMono,
    ISpeechPhraseElement, DISPID_SRSAudioStatus,
    DISPID_SADefaultFormat, SPPS_RESERVED3, SASClosed,
    SVEStartInputStream, eLEXTYPE_RESERVED6, _RemotableHandle,
    DISPID_SGRInitialState, DISPID_SVEPhoneme, eLEXTYPE_PRIVATE4,
    SPAO_NONE, ISpAudio, SAFTText, DISPID_SPARecoResult,
    SpeechRegistryLocalMachineRoot, SRATopLevel, SPFM_OPEN_READWRITE,
    eLEXTYPE_PRIVATE5, SGSEnabled, SPSHT_EMAIL, SASStop,
    DISPID_SGRAddResource, SAFT44kHz8BitMono, eLEXTYPE_PRIVATE9,
    SPEI_MIN_TTS, SP_VISEME_18, DISPID_SOTCSetId, SECFIgnoreKanaType,
    SPEI_ACTIVE_CATEGORY_CHANGED, DISPID_SRCESoundEnd, SPEI_RESERVED3,
    DISPID_SMSALineId, SAFTCCITT_uLaw_44kHzMono,
    DISPID_SDKGetlongValue, DISPID_SVAudioOutput, SRTReSent,
    SPRST_ACTIVE, SPINTERFERENCE_NOISE, DISPID_SOTs_NewEnum,
    SPPS_RESERVED4, SRSEDone, _ULARGE_INTEGER,
    DISPID_SRGCmdLoadFromProprietaryGrammar, DISPID_SRGetRecognizers,
    SPSMF_SRGS_SAPIPROPERTIES, SVP_13, ISpeechRecoResultDispatch,
    DISPID_SRRTStreamTime, DISPID_SPIElements, SAFT32kHz8BitMono,
    SP_VISEME_1, SAFT22kHz16BitStereo, SPPS_Noncontent,
    DISPID_SPEEngineConfidence, SVP_7, SpeechUserTraining,
    SPDKL_CurrentConfig, COMMETHOD, DISPID_SLGetPronunciations, SVP_1,
    DISPID_SDKSetStringValue, ISpNotifySink, DISPID_SDKSetBinaryValue,
    DISPID_SRGCmdLoadFromFile, SAFT22kHz8BitStereo, DISPID_SRGRules,
    SPVPRI_ALERT, SAFT8kHz8BitStereo, DISPID_SGRsCommitAndSave,
    ISpRecoResult, SWPUnknownWordUnpronounceable,
    SAFTCCITT_ALaw_11kHzStereo, SVSFParseMask, SVPNormal,
    SPEI_HYPOTHESIS, SAFTDefault, DISPID_SASetState, SVSFParseSsml,
    DISPID_SRCState, DISPID_SPPsItem, SAFT16kHz16BitMono,
    SP_VISEME_19, DISPID_SGRSAddSpecialTransition, tagSTATSTG,
    DISPID_SGRSTPropertyName, SpMMAudioOut, SVP_10,
    SWPKnownWordPronounceable, SpWaveFormatEx, SP_VISEME_14,
    DISPID_SBSFormat, SVSFPersistXML, IInternetSecurityMgrSite,
    SpeechAudioVolume, DISPID_SRRAlternates, ISpeechRecoResultTimes
)


class DISPID_SpeechXMLRecoResult(IntFlag):
    DISPID_SRRGetXMLResult = 10
    DISPID_SRRGetXMLErrorInfo = 11


class SPXMLRESULTOPTIONS(IntFlag):
    SPXRO_SML = 0
    SPXRO_Alternates_SML = 1


class DISPID_SpeechRecoResult2(IntFlag):
    DISPID_SRRSetTextFeedback = 12


class DISPID_SpeechPhraseBuilder(IntFlag):
    DISPID_SPPBRestorePhraseFromMemory = 1


class DISPID_SpeechRecoResultTimes(IntFlag):
    DISPID_SRRTStreamTime = 1
    DISPID_SRRTLength = 2
    DISPID_SRRTTickCount = 3
    DISPID_SRRTOffsetFromStart = 4


class SPDATAKEYLOCATION(IntFlag):
    SPDKL_DefaultLocation = 0
    SPDKL_CurrentUser = 1
    SPDKL_LocalMachine = 2
    SPDKL_CurrentConfig = 5


class DISPID_SpeechPhraseAlternate(IntFlag):
    DISPID_SPARecoResult = 1
    DISPID_SPAStartElementInResult = 2
    DISPID_SPANumberOfElementsInResult = 3
    DISPID_SPAPhraseInfo = 4
    DISPID_SPACommit = 5


class DISPID_SpeechPhraseAlternates(IntFlag):
    DISPID_SPAsCount = 1
    DISPID_SPAsItem = 0
    DISPID_SPAs_NewEnum = -4


class DISPID_SpeechPhraseInfo(IntFlag):
    DISPID_SPILanguageId = 1
    DISPID_SPIGrammarId = 2
    DISPID_SPIStartTime = 3
    DISPID_SPIAudioStreamPosition = 4
    DISPID_SPIAudioSizeBytes = 5
    DISPID_SPIRetainedSizeBytes = 6
    DISPID_SPIAudioSizeTime = 7
    DISPID_SPIRule = 8
    DISPID_SPIProperties = 9
    DISPID_SPIElements = 10
    DISPID_SPIReplacements = 11
    DISPID_SPIEngineId = 12
    DISPID_SPIEnginePrivateData = 13
    DISPID_SPISaveToMemory = 14
    DISPID_SPIGetText = 15
    DISPID_SPIGetDisplayAttributes = 16


class DISPID_SpeechPhraseElement(IntFlag):
    DISPID_SPEAudioTimeOffset = 1
    DISPID_SPEAudioSizeTime = 2
    DISPID_SPEAudioStreamOffset = 3
    DISPID_SPEAudioSizeBytes = 4
    DISPID_SPERetainedStreamOffset = 5
    DISPID_SPERetainedSizeBytes = 6
    DISPID_SPEDisplayText = 7
    DISPID_SPELexicalForm = 8
    DISPID_SPEPronunciation = 9
    DISPID_SPEDisplayAttributes = 10
    DISPID_SPERequiredConfidence = 11
    DISPID_SPEActualConfidence = 12
    DISPID_SPEEngineConfidence = 13


class DISPID_SpeechPhraseElements(IntFlag):
    DISPID_SPEsCount = 1
    DISPID_SPEsItem = 0
    DISPID_SPEs_NewEnum = -4


class DISPID_SpeechPhraseReplacement(IntFlag):
    DISPID_SPRDisplayAttributes = 1
    DISPID_SPRText = 2
    DISPID_SPRFirstElement = 3
    DISPID_SPRNumberOfElements = 4


class DISPID_SpeechPhraseReplacements(IntFlag):
    DISPID_SPRsCount = 1
    DISPID_SPRsItem = 0
    DISPID_SPRs_NewEnum = -4


class DISPID_SpeechPhraseProperty(IntFlag):
    DISPID_SPPName = 1
    DISPID_SPPId = 2
    DISPID_SPPValue = 3
    DISPID_SPPFirstElement = 4
    DISPID_SPPNumberOfElements = 5
    DISPID_SPPEngineConfidence = 6
    DISPID_SPPConfidence = 7
    DISPID_SPPParent = 8
    DISPID_SPPChildren = 9


class DISPID_SpeechPhraseProperties(IntFlag):
    DISPID_SPPsCount = 1
    DISPID_SPPsItem = 0
    DISPID_SPPs_NewEnum = -4


class DISPID_SpeechPhraseRule(IntFlag):
    DISPID_SPRuleName = 1
    DISPID_SPRuleId = 2
    DISPID_SPRuleFirstElement = 3
    DISPID_SPRuleNumberOfElements = 4
    DISPID_SPRuleParent = 5
    DISPID_SPRuleChildren = 6
    DISPID_SPRuleConfidence = 7
    DISPID_SPRuleEngineConfidence = 8


class SpeechInterference(IntFlag):
    SINone = 0
    SINoise = 1
    SINoSignal = 2
    SITooLoud = 3
    SITooQuiet = 4
    SITooFast = 5
    SITooSlow = 6


class SpeechRecoEvents(IntFlag):
    SREStreamEnd = 1
    SRESoundStart = 2
    SRESoundEnd = 4
    SREPhraseStart = 8
    SRERecognition = 16
    SREHypothesis = 32
    SREBookmark = 64
    SREPropertyNumChange = 128
    SREPropertyStringChange = 256
    SREFalseRecognition = 512
    SREInterference = 1024
    SRERequestUI = 2048
    SREStateChange = 4096
    SREAdaptation = 8192
    SREStreamStart = 16384
    SRERecoOtherContext = 32768
    SREAudioLevel = 65536
    SREPrivate = 262144
    SREAllEvents = 393215


class SpeechRecoContextState(IntFlag):
    SRCS_Disabled = 0
    SRCS_Enabled = 1


class SpeechRetainedAudioOptions(IntFlag):
    SRAONone = 0
    SRAORetainAudio = 1


class SpeechBookmarkOptions(IntFlag):
    SBONone = 0
    SBOPause = 1


class DISPID_SpeechPhraseRules(IntFlag):
    DISPID_SPRulesCount = 1
    DISPID_SPRulesItem = 0
    DISPID_SPRules_NewEnum = -4


class DISPID_SpeechLexicon(IntFlag):
    DISPID_SLGenerationId = 1
    DISPID_SLGetWords = 2
    DISPID_SLAddPronunciation = 3
    DISPID_SLAddPronunciationByPhoneIds = 4
    DISPID_SLRemovePronunciation = 5
    DISPID_SLRemovePronunciationByPhoneIds = 6
    DISPID_SLGetPronunciations = 7
    DISPID_SLGetGenerationChange = 8


class DISPID_SpeechLexiconWords(IntFlag):
    DISPID_SLWsCount = 1
    DISPID_SLWsItem = 0
    DISPID_SLWs_NewEnum = -4


class DISPID_SpeechLexiconWord(IntFlag):
    DISPID_SLWLangId = 1
    DISPID_SLWType = 2
    DISPID_SLWWord = 3
    DISPID_SLWPronunciations = 4


class DISPID_SpeechLexiconProns(IntFlag):
    DISPID_SLPsCount = 1
    DISPID_SLPsItem = 0
    DISPID_SLPs_NewEnum = -4


class DISPID_SpeechLexiconPronunciation(IntFlag):
    DISPID_SLPType = 1
    DISPID_SLPLangId = 2
    DISPID_SLPPartOfSpeech = 3
    DISPID_SLPPhoneIds = 4
    DISPID_SLPSymbolic = 5


class SpeechGrammarState(IntFlag):
    SGSEnabled = 1
    SGSDisabled = 0
    SGSExclusive = 3


class DISPID_SpeechPhoneConverter(IntFlag):
    DISPID_SPCLangId = 1
    DISPID_SPCPhoneToId = 2
    DISPID_SPCIdToPhone = 3


class SpeechAudioFormatType(IntFlag):
    SAFTDefault = -1
    SAFTNoAssignedFormat = 0
    SAFTText = 1
    SAFTNonStandardFormat = 2
    SAFTExtendedAudioFormat = 3
    SAFT8kHz8BitMono = 4
    SAFT8kHz8BitStereo = 5
    SAFT8kHz16BitMono = 6
    SAFT8kHz16BitStereo = 7
    SAFT11kHz8BitMono = 8
    SAFT11kHz8BitStereo = 9
    SAFT11kHz16BitMono = 10
    SAFT11kHz16BitStereo = 11
    SAFT12kHz8BitMono = 12
    SAFT12kHz8BitStereo = 13
    SAFT12kHz16BitMono = 14
    SAFT12kHz16BitStereo = 15
    SAFT16kHz8BitMono = 16
    SAFT16kHz8BitStereo = 17
    SAFT16kHz16BitMono = 18
    SAFT16kHz16BitStereo = 19
    SAFT22kHz8BitMono = 20
    SAFT22kHz8BitStereo = 21
    SAFT22kHz16BitMono = 22
    SAFT22kHz16BitStereo = 23
    SAFT24kHz8BitMono = 24
    SAFT24kHz8BitStereo = 25
    SAFT24kHz16BitMono = 26
    SAFT24kHz16BitStereo = 27
    SAFT32kHz8BitMono = 28
    SAFT32kHz8BitStereo = 29
    SAFT32kHz16BitMono = 30
    SAFT32kHz16BitStereo = 31
    SAFT44kHz8BitMono = 32
    SAFT44kHz8BitStereo = 33
    SAFT44kHz16BitMono = 34
    SAFT44kHz16BitStereo = 35
    SAFT48kHz8BitMono = 36
    SAFT48kHz8BitStereo = 37
    SAFT48kHz16BitMono = 38
    SAFT48kHz16BitStereo = 39
    SAFTTrueSpeech_8kHz1BitMono = 40
    SAFTCCITT_ALaw_8kHzMono = 41
    SAFTCCITT_ALaw_8kHzStereo = 42
    SAFTCCITT_ALaw_11kHzMono = 43
    SAFTCCITT_ALaw_11kHzStereo = 44
    SAFTCCITT_ALaw_22kHzMono = 45
    SAFTCCITT_ALaw_22kHzStereo = 46
    SAFTCCITT_ALaw_44kHzMono = 47
    SAFTCCITT_ALaw_44kHzStereo = 48
    SAFTCCITT_uLaw_8kHzMono = 49
    SAFTCCITT_uLaw_8kHzStereo = 50
    SAFTCCITT_uLaw_11kHzMono = 51
    SAFTCCITT_uLaw_11kHzStereo = 52
    SAFTCCITT_uLaw_22kHzMono = 53
    SAFTCCITT_uLaw_22kHzStereo = 54
    SAFTCCITT_uLaw_44kHzMono = 55
    SAFTCCITT_uLaw_44kHzStereo = 56
    SAFTADPCM_8kHzMono = 57
    SAFTADPCM_8kHzStereo = 58
    SAFTADPCM_11kHzMono = 59
    SAFTADPCM_11kHzStereo = 60
    SAFTADPCM_22kHzMono = 61
    SAFTADPCM_22kHzStereo = 62
    SAFTADPCM_44kHzMono = 63
    SAFTADPCM_44kHzStereo = 64
    SAFTGSM610_8kHzMono = 65
    SAFTGSM610_11kHzMono = 66
    SAFTGSM610_22kHzMono = 67
    SAFTGSM610_44kHzMono = 68


class SpeechStreamFileMode(IntFlag):
    SSFMOpenForRead = 0
    SSFMOpenReadWrite = 1
    SSFMCreate = 2
    SSFMCreateForWrite = 3


class SpeechRunState(IntFlag):
    SRSEDone = 1
    SRSEIsSpeaking = 2


class _SPAUDIOSTATE(IntFlag):
    SPAS_CLOSED = 0
    SPAS_STOP = 1
    SPAS_PAUSE = 2
    SPAS_RUN = 3


class SpeechStreamSeekPositionType(IntFlag):
    SSSPTRelativeToStart = 0
    SSSPTRelativeToCurrentPosition = 1
    SSSPTRelativeToEnd = 2


class SPFILEMODE(IntFlag):
    SPFM_OPEN_READONLY = 0
    SPFM_OPEN_READWRITE = 1
    SPFM_CREATE = 2
    SPFM_CREATE_ALWAYS = 3
    SPFM_NUM_MODES = 4


class SPVPRIORITY(IntFlag):
    SPVPRI_NORMAL = 0
    SPVPRI_ALERT = 1
    SPVPRI_OVER = 2


class SPEVENTENUM(IntFlag):
    SPEI_UNDEFINED = 0
    SPEI_START_INPUT_STREAM = 1
    SPEI_END_INPUT_STREAM = 2
    SPEI_VOICE_CHANGE = 3
    SPEI_TTS_BOOKMARK = 4
    SPEI_WORD_BOUNDARY = 5
    SPEI_PHONEME = 6
    SPEI_SENTENCE_BOUNDARY = 7
    SPEI_VISEME = 8
    SPEI_TTS_AUDIO_LEVEL = 9
    SPEI_TTS_PRIVATE = 15
    SPEI_MIN_TTS = 1
    SPEI_MAX_TTS = 15
    SPEI_END_SR_STREAM = 34
    SPEI_SOUND_START = 35
    SPEI_SOUND_END = 36
    SPEI_PHRASE_START = 37
    SPEI_RECOGNITION = 38
    SPEI_HYPOTHESIS = 39
    SPEI_SR_BOOKMARK = 40
    SPEI_PROPERTY_NUM_CHANGE = 41
    SPEI_PROPERTY_STRING_CHANGE = 42
    SPEI_FALSE_RECOGNITION = 43
    SPEI_INTERFERENCE = 44
    SPEI_REQUEST_UI = 45
    SPEI_RECO_STATE_CHANGE = 46
    SPEI_ADAPTATION = 47
    SPEI_START_SR_STREAM = 48
    SPEI_RECO_OTHER_CONTEXT = 49
    SPEI_SR_AUDIO_LEVEL = 50
    SPEI_SR_RETAINEDAUDIO = 51
    SPEI_SR_PRIVATE = 52
    SPEI_ACTIVE_CATEGORY_CHANGED = 53
    SPEI_RESERVED5 = 54
    SPEI_RESERVED6 = 55
    SPEI_MIN_SR = 34
    SPEI_MAX_SR = 55
    SPEI_RESERVED1 = 30
    SPEI_RESERVED2 = 33
    SPEI_RESERVED3 = 63


class SpeechTokenContext(IntFlag):
    STCInprocServer = 1
    STCInprocHandler = 2
    STCLocalServer = 4
    STCRemoteServer = 16
    STCAll = 23


class SpeechTokenShellFolder(IntFlag):
    STSF_AppData = 26
    STSF_LocalAppData = 28
    STSF_CommonAppData = 35
    STSF_FlagCreate = 32768


class SPAUDIOOPTIONS(IntFlag):
    SPAO_NONE = 0
    SPAO_RETAIN_AUDIO = 1


class SPBOOKMARKOPTIONS(IntFlag):
    SPBO_NONE = 0
    SPBO_PAUSE = 1
    SPBO_AHEAD = 2
    SPBO_TIME_UNITS = 4


class SPCONTEXTSTATE(IntFlag):
    SPCS_DISABLED = 0
    SPCS_ENABLED = 1


class SPRECOSTATE(IntFlag):
    SPRST_INACTIVE = 0
    SPRST_ACTIVE = 1
    SPRST_ACTIVE_ALWAYS = 2
    SPRST_INACTIVE_WITH_PURGE = 3
    SPRST_NUM_STATES = 4


class SPWAVEFORMATTYPE(IntFlag):
    SPWF_INPUT = 0
    SPWF_SRENGINE = 1


class SPVISEMES(IntFlag):
    SP_VISEME_0 = 0
    SP_VISEME_1 = 1
    SP_VISEME_2 = 2
    SP_VISEME_3 = 3
    SP_VISEME_4 = 4
    SP_VISEME_5 = 5
    SP_VISEME_6 = 6
    SP_VISEME_7 = 7
    SP_VISEME_8 = 8
    SP_VISEME_9 = 9
    SP_VISEME_10 = 10
    SP_VISEME_11 = 11
    SP_VISEME_12 = 12
    SP_VISEME_13 = 13
    SP_VISEME_14 = 14
    SP_VISEME_15 = 15
    SP_VISEME_16 = 16
    SP_VISEME_17 = 17
    SP_VISEME_18 = 18
    SP_VISEME_19 = 19
    SP_VISEME_20 = 20
    SP_VISEME_21 = 21


class SpeechDataKeyLocation(IntFlag):
    SDKLDefaultLocation = 0
    SDKLCurrentUser = 1
    SDKLLocalMachine = 2
    SDKLCurrentConfig = 5


class SpeechVoiceEvents(IntFlag):
    SVEStartInputStream = 2
    SVEEndInputStream = 4
    SVEVoiceChange = 8
    SVEBookmark = 16
    SVEWordBoundary = 32
    SVEPhoneme = 64
    SVESentenceBoundary = 128
    SVEViseme = 256
    SVEAudioLevel = 512
    SVEPrivate = 32768
    SVEAllEvents = 33790


class SpeechVoicePriority(IntFlag):
    SVPNormal = 0
    SVPAlert = 1
    SVPOver = 2


class SpeechAudioState(IntFlag):
    SASClosed = 0
    SASStop = 1
    SASPause = 2
    SASRun = 3


class SPGRAMMARWORDTYPE(IntFlag):
    SPWT_DISPLAY = 0
    SPWT_LEXICAL = 1
    SPWT_PRONUNCIATION = 2
    SPWT_LEXICAL_NO_SPECIAL_CHARS = 3


class SPLOADOPTIONS(IntFlag):
    SPLO_STATIC = 0
    SPLO_DYNAMIC = 1


class SPRULESTATE(IntFlag):
    SPRS_INACTIVE = 0
    SPRS_ACTIVE = 1
    SPRS_ACTIVE_WITH_AUTO_PAUSE = 3
    SPRS_ACTIVE_USER_DELIMITED = 4


class SPWORDPRONOUNCEABLE(IntFlag):
    SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE = 0
    SPWP_UNKNOWN_WORD_PRONOUNCEABLE = 1
    SPWP_KNOWN_WORD_PRONOUNCEABLE = 2


class SPGRAMMARSTATE(IntFlag):
    SPGS_DISABLED = 0
    SPGS_ENABLED = 1
    SPGS_EXCLUSIVE = 3


class SpeechRecognitionType(IntFlag):
    SRTStandard = 0
    SRTAutopause = 1
    SRTEmulated = 2
    SRTSMLTimeout = 4
    SRTExtendableParse = 8
    SRTReSent = 16


class SpeechRecognizerState(IntFlag):
    SRSInactive = 0
    SRSActive = 1
    SRSActiveAlways = 2
    SRSInactiveWithPurge = 3


class SPINTERFERENCE(IntFlag):
    SPINTERFERENCE_NONE = 0
    SPINTERFERENCE_NOISE = 1
    SPINTERFERENCE_NOSIGNAL = 2
    SPINTERFERENCE_TOOLOUD = 3
    SPINTERFERENCE_TOOQUIET = 4
    SPINTERFERENCE_TOOFAST = 5
    SPINTERFERENCE_TOOSLOW = 6
    SPINTERFERENCE_LATENCY_WARNING = 7
    SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN = 8
    SPINTERFERENCE_LATENCY_TRUNCATE_END = 9


class SPADAPTATIONRELEVANCE(IntFlag):
    SPAR_Unknown = 0
    SPAR_Low = 1
    SPAR_Medium = 2
    SPAR_High = 3


class SPCATEGORYTYPE(IntFlag):
    SPCT_COMMAND = 0
    SPCT_DICTATION = 1
    SPCT_SLEEP = 2
    SPCT_SUB_COMMAND = 3
    SPCT_SUB_DICTATION = 4


class SPPARTOFSPEECH(IntFlag):
    SPPS_NotOverriden = -1
    SPPS_Unknown = 0
    SPPS_Noun = 4096
    SPPS_Verb = 8192
    SPPS_Modifier = 12288
    SPPS_Function = 16384
    SPPS_Interjection = 20480
    SPPS_Noncontent = 24576
    SPPS_LMA = 28672
    SPPS_SuppressWord = 61440


class SPLEXICONTYPE(IntFlag):
    eLEXTYPE_USER = 1
    eLEXTYPE_APP = 2
    eLEXTYPE_VENDORLEXICON = 4
    eLEXTYPE_LETTERTOSOUND = 8
    eLEXTYPE_MORPHOLOGY = 16
    eLEXTYPE_RESERVED4 = 32
    eLEXTYPE_USER_SHORTCUT = 64
    eLEXTYPE_RESERVED6 = 128
    eLEXTYPE_RESERVED7 = 256
    eLEXTYPE_RESERVED8 = 512
    eLEXTYPE_RESERVED9 = 1024
    eLEXTYPE_RESERVED10 = 2048
    eLEXTYPE_PRIVATE1 = 4096
    eLEXTYPE_PRIVATE2 = 8192
    eLEXTYPE_PRIVATE3 = 16384
    eLEXTYPE_PRIVATE4 = 32768
    eLEXTYPE_PRIVATE5 = 65536
    eLEXTYPE_PRIVATE6 = 131072
    eLEXTYPE_PRIVATE7 = 262144
    eLEXTYPE_PRIVATE8 = 524288
    eLEXTYPE_PRIVATE9 = 1048576
    eLEXTYPE_PRIVATE10 = 2097152
    eLEXTYPE_PRIVATE11 = 4194304
    eLEXTYPE_PRIVATE12 = 8388608
    eLEXTYPE_PRIVATE13 = 16777216
    eLEXTYPE_PRIVATE14 = 33554432
    eLEXTYPE_PRIVATE15 = 67108864
    eLEXTYPE_PRIVATE16 = 134217728
    eLEXTYPE_PRIVATE17 = 268435456
    eLEXTYPE_PRIVATE18 = 536870912
    eLEXTYPE_PRIVATE19 = 1073741824
    eLEXTYPE_PRIVATE20 = -2147483648


class SPWORDTYPE(IntFlag):
    eWORDTYPE_ADDED = 1
    eWORDTYPE_DELETED = 2


class SpeechLoadOption(IntFlag):
    SLOStatic = 0
    SLODynamic = 1


class SpeechRuleState(IntFlag):
    SGDSInactive = 0
    SGDSActive = 1
    SGDSActiveWithAutoPause = 3
    SGDSActiveUserDelimited = 4


class SpeechWordPronounceable(IntFlag):
    SWPUnknownWordUnpronounceable = 0
    SWPUnknownWordPronounceable = 1
    SWPKnownWordPronounceable = 2


class SpeechFormatType(IntFlag):
    SFTInput = 0
    SFTSREngine = 1


class SPSEMANTICFORMAT(IntFlag):
    SPSMF_SAPI_PROPERTIES = 0
    SPSMF_SRGS_SEMANTICINTERPRETATION_MS = 1
    SPSMF_SRGS_SAPIPROPERTIES = 2
    SPSMF_UPS = 4
    SPSMF_SRGS_SEMANTICINTERPRETATION_W3C = 8


class SPSHORTCUTTYPE(IntFlag):
    SPSHT_NotOverriden = -1
    SPSHT_Unknown = 0
    SPSHT_EMAIL = 4096
    SPSHT_OTHER = 8192
    SPPS_RESERVED1 = 12288
    SPPS_RESERVED2 = 16384
    SPPS_RESERVED3 = 20480
    SPPS_RESERVED4 = 61440


class SpeechRuleAttributes(IntFlag):
    SRATopLevel = 1
    SRADefaultToActive = 2
    SRAExport = 4
    SRAImport = 8
    SRAInterpreter = 16
    SRADynamic = 32
    SRARoot = 64


class SpeechEngineConfidence(IntFlag):
    SECLowConfidence = -1
    SECNormalConfidence = 0
    SECHighConfidence = 1


class SpeechDisplayAttributes(IntFlag):
    SDA_No_Trailing_Space = 0
    SDA_One_Trailing_Space = 2
    SDA_Two_Trailing_Spaces = 4
    SDA_Consume_Leading_Spaces = 8


class SpeechGrammarRuleStateTransitionType(IntFlag):
    SGRSTTEpsilon = 0
    SGRSTTWord = 1
    SGRSTTRule = 2
    SGRSTTDictation = 3
    SGRSTTWildcard = 4
    SGRSTTTextBuffer = 5


class SpeechDiscardType(IntFlag):
    SDTProperty = 1
    SDTReplacement = 2
    SDTRule = 4
    SDTDisplayText = 8
    SDTLexicalForm = 16
    SDTPronunciation = 32
    SDTAudio = 64
    SDTAlternates = 128
    SDTAll = 255


class SpeechGrammarWordType(IntFlag):
    SGDisplay = 0
    SGLexical = 1
    SGPronounciation = 2
    SGLexicalNoSpecialChars = 3


class SpeechSpecialTransitionType(IntFlag):
    SSTTWildcard = 1
    SSTTDictation = 2
    SSTTTextBuffer = 3


class SpeechLexiconType(IntFlag):
    SLTUser = 1
    SLTApp = 2


class SpeechWordType(IntFlag):
    SWTAdded = 1
    SWTDeleted = 2


class SpeechVoiceSpeakFlags(IntFlag):
    SVSFDefault = 0
    SVSFlagsAsync = 1
    SVSFPurgeBeforeSpeak = 2
    SVSFIsFilename = 4
    SVSFIsXML = 8
    SVSFIsNotXML = 16
    SVSFPersistXML = 32
    SVSFNLPSpeakPunc = 64
    SVSFParseSapi = 128
    SVSFParseSsml = 256
    SVSFParseAutodetect = 0
    SVSFNLPMask = 64
    SVSFParseMask = 384
    SVSFVoiceMask = 511
    SVSFUnusedFlags = -512


class SpeechPartOfSpeech(IntFlag):
    SPSNotOverriden = -1
    SPSUnknown = 0
    SPSNoun = 4096
    SPSVerb = 8192
    SPSModifier = 12288
    SPSFunction = 16384
    SPSInterjection = 20480
    SPSLMA = 28672
    SPSSuppressWord = 61440


class SpeechVisemeFeature(IntFlag):
    SVF_None = 0
    SVF_Stressed = 1
    SVF_Emphasis = 2


class SpeechVisemeType(IntFlag):
    SVP_0 = 0
    SVP_1 = 1
    SVP_2 = 2
    SVP_3 = 3
    SVP_4 = 4
    SVP_5 = 5
    SVP_6 = 6
    SVP_7 = 7
    SVP_8 = 8
    SVP_9 = 9
    SVP_10 = 10
    SVP_11 = 11
    SVP_12 = 12
    SVP_13 = 13
    SVP_14 = 14
    SVP_15 = 15
    SVP_16 = 16
    SVP_17 = 17
    SVP_18 = 18
    SVP_19 = 19
    SVP_20 = 20
    SVP_21 = 21


class DISPID_SpeechDataKey(IntFlag):
    DISPID_SDKSetBinaryValue = 1
    DISPID_SDKGetBinaryValue = 2
    DISPID_SDKSetStringValue = 3
    DISPID_SDKGetStringValue = 4
    DISPID_SDKSetLongValue = 5
    DISPID_SDKGetlongValue = 6
    DISPID_SDKOpenKey = 7
    DISPID_SDKCreateKey = 8
    DISPID_SDKDeleteKey = 9
    DISPID_SDKDeleteValue = 10
    DISPID_SDKEnumKeys = 11
    DISPID_SDKEnumValues = 12


class DISPID_SpeechObjectToken(IntFlag):
    DISPID_SOTId = 1
    DISPID_SOTDataKey = 2
    DISPID_SOTCategory = 3
    DISPID_SOTGetDescription = 4
    DISPID_SOTSetId = 5
    DISPID_SOTGetAttribute = 6
    DISPID_SOTCreateInstance = 7
    DISPID_SOTRemove = 8
    DISPID_SOTGetStorageFileName = 9
    DISPID_SOTRemoveStorageFileName = 10
    DISPID_SOTIsUISupported = 11
    DISPID_SOTDisplayUI = 12
    DISPID_SOTMatchesAttributes = 13


class DISPID_SpeechObjectTokens(IntFlag):
    DISPID_SOTsCount = 1
    DISPID_SOTsItem = 0
    DISPID_SOTs_NewEnum = -4


class DISPID_SpeechObjectTokenCategory(IntFlag):
    DISPID_SOTCId = 1
    DISPID_SOTCDefault = 2
    DISPID_SOTCSetId = 3
    DISPID_SOTCGetDataKey = 4
    DISPID_SOTCEnumerateTokens = 5


class DISPID_SpeechAudioFormat(IntFlag):
    DISPID_SAFType = 1
    DISPID_SAFGuid = 2
    DISPID_SAFGetWaveFormatEx = 3
    DISPID_SAFSetWaveFormatEx = 4


class DISPID_SpeechBaseStream(IntFlag):
    DISPID_SBSFormat = 1
    DISPID_SBSRead = 2
    DISPID_SBSWrite = 3
    DISPID_SBSSeek = 4


class DISPID_SpeechAudio(IntFlag):
    DISPID_SAStatus = 200
    DISPID_SABufferInfo = 201
    DISPID_SADefaultFormat = 202
    DISPID_SAVolume = 203
    DISPID_SABufferNotifySize = 204
    DISPID_SAEventHandle = 205
    DISPID_SASetState = 206


class DISPID_SpeechMMSysAudio(IntFlag):
    DISPID_SMSADeviceId = 300
    DISPID_SMSALineId = 301
    DISPID_SMSAMMHandle = 302


class DISPID_SpeechFileStream(IntFlag):
    DISPID_SFSOpen = 100
    DISPID_SFSClose = 101


class DISPID_SpeechCustomStream(IntFlag):
    DISPID_SCSBaseStream = 100


class DISPID_SpeechMemoryStream(IntFlag):
    DISPID_SMSSetData = 100
    DISPID_SMSGetData = 101


class DISPID_SpeechAudioStatus(IntFlag):
    DISPID_SASFreeBufferSpace = 1
    DISPID_SASNonBlockingIO = 2
    DISPID_SASState = 3
    DISPID_SASCurrentSeekPosition = 4
    DISPID_SASCurrentDevicePosition = 5


class DISPID_SpeechAudioBufferInfo(IntFlag):
    DISPID_SABIMinNotification = 1
    DISPID_SABIBufferSize = 2
    DISPID_SABIEventBias = 3


class DISPID_SpeechWaveFormatEx(IntFlag):
    DISPID_SWFEFormatTag = 1
    DISPID_SWFEChannels = 2
    DISPID_SWFESamplesPerSec = 3
    DISPID_SWFEAvgBytesPerSec = 4
    DISPID_SWFEBlockAlign = 5
    DISPID_SWFEBitsPerSample = 6
    DISPID_SWFEExtraData = 7


class DISPID_SpeechVoice(IntFlag):
    DISPID_SVStatus = 1
    DISPID_SVVoice = 2
    DISPID_SVAudioOutput = 3
    DISPID_SVAudioOutputStream = 4
    DISPID_SVRate = 5
    DISPID_SVVolume = 6
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet = 7
    DISPID_SVEventInterests = 8
    DISPID_SVPriority = 9
    DISPID_SVAlertBoundary = 10
    DISPID_SVSyncronousSpeakTimeout = 11
    DISPID_SVSpeak = 12
    DISPID_SVSpeakStream = 13
    DISPID_SVPause = 14
    DISPID_SVResume = 15
    DISPID_SVSkip = 16
    DISPID_SVGetVoices = 17
    DISPID_SVGetAudioOutputs = 18
    DISPID_SVWaitUntilDone = 19
    DISPID_SVSpeakCompleteEvent = 20
    DISPID_SVIsUISupported = 21
    DISPID_SVDisplayUI = 22


class DISPID_SpeechVoiceStatus(IntFlag):
    DISPID_SVSCurrentStreamNumber = 1
    DISPID_SVSLastStreamNumberQueued = 2
    DISPID_SVSLastResult = 3
    DISPID_SVSRunningState = 4
    DISPID_SVSInputWordPosition = 5
    DISPID_SVSInputWordLength = 6
    DISPID_SVSInputSentencePosition = 7
    DISPID_SVSInputSentenceLength = 8
    DISPID_SVSLastBookmark = 9
    DISPID_SVSLastBookmarkId = 10
    DISPID_SVSPhonemeId = 11
    DISPID_SVSVisemeId = 12


class DISPID_SpeechVoiceEvent(IntFlag):
    DISPID_SVEStreamStart = 1
    DISPID_SVEStreamEnd = 2
    DISPID_SVEVoiceChange = 3
    DISPID_SVEBookmark = 4
    DISPID_SVEWord = 5
    DISPID_SVEPhoneme = 6
    DISPID_SVESentenceBoundary = 7
    DISPID_SVEViseme = 8
    DISPID_SVEAudioLevel = 9
    DISPID_SVEEnginePrivate = 10


class DISPID_SpeechRecognizer(IntFlag):
    DISPID_SRRecognizer = 1
    DISPID_SRAllowAudioInputFormatChangesOnNextSet = 2
    DISPID_SRAudioInput = 3
    DISPID_SRAudioInputStream = 4
    DISPID_SRIsShared = 5
    DISPID_SRState = 6
    DISPID_SRStatus = 7
    DISPID_SRProfile = 8
    DISPID_SREmulateRecognition = 9
    DISPID_SRCreateRecoContext = 10
    DISPID_SRGetFormat = 11
    DISPID_SRSetPropertyNumber = 12
    DISPID_SRGetPropertyNumber = 13
    DISPID_SRSetPropertyString = 14
    DISPID_SRGetPropertyString = 15
    DISPID_SRIsUISupported = 16
    DISPID_SRDisplayUI = 17
    DISPID_SRGetRecognizers = 18
    DISPID_SVGetAudioInputs = 19
    DISPID_SVGetProfiles = 20


class SpeechEmulationCompareFlags(IntFlag):
    SECFIgnoreCase = 1
    SECFIgnoreKanaType = 65536
    SECFIgnoreWidth = 131072
    SECFNoSpecialChars = 536870912
    SECFEmulateResult = 1073741824
    SECFDefault = 196609


class DISPID_SpeechRecognizerStatus(IntFlag):
    DISPID_SRSAudioStatus = 1
    DISPID_SRSCurrentStreamPosition = 2
    DISPID_SRSCurrentStreamNumber = 3
    DISPID_SRSNumberOfActiveRules = 4
    DISPID_SRSClsidEngine = 5
    DISPID_SRSSupportedLanguages = 6


class DISPID_SpeechRecoContext(IntFlag):
    DISPID_SRCRecognizer = 1
    DISPID_SRCAudioInInterferenceStatus = 2
    DISPID_SRCRequestedUIType = 3
    DISPID_SRCVoice = 4
    DISPID_SRAllowVoiceFormatMatchingOnNextSet = 5
    DISPID_SRCVoicePurgeEvent = 6
    DISPID_SRCEventInterests = 7
    DISPID_SRCCmdMaxAlternates = 8
    DISPID_SRCState = 9
    DISPID_SRCRetainedAudio = 10
    DISPID_SRCRetainedAudioFormat = 11
    DISPID_SRCPause = 12
    DISPID_SRCResume = 13
    DISPID_SRCCreateGrammar = 14
    DISPID_SRCCreateResultFromMemory = 15
    DISPID_SRCBookmark = 16
    DISPID_SRCSetAdaptationData = 17


class DISPIDSPRG(IntFlag):
    DISPID_SRGId = 1
    DISPID_SRGRecoContext = 2
    DISPID_SRGState = 3
    DISPID_SRGRules = 4
    DISPID_SRGReset = 5
    DISPID_SRGCommit = 6
    DISPID_SRGCmdLoadFromFile = 7
    DISPID_SRGCmdLoadFromObject = 8
    DISPID_SRGCmdLoadFromResource = 9
    DISPID_SRGCmdLoadFromMemory = 10
    DISPID_SRGCmdLoadFromProprietaryGrammar = 11
    DISPID_SRGCmdSetRuleState = 12
    DISPID_SRGCmdSetRuleIdState = 13
    DISPID_SRGDictationLoad = 14
    DISPID_SRGDictationUnload = 15
    DISPID_SRGDictationSetState = 16
    DISPID_SRGSetWordSequenceData = 17
    DISPID_SRGSetTextSelection = 18
    DISPID_SRGIsPronounceable = 19


class DISPID_SpeechRecoContextEvents(IntFlag):
    DISPID_SRCEStartStream = 1
    DISPID_SRCEEndStream = 2
    DISPID_SRCEBookmark = 3
    DISPID_SRCESoundStart = 4
    DISPID_SRCESoundEnd = 5
    DISPID_SRCEPhraseStart = 6
    DISPID_SRCERecognition = 7
    DISPID_SRCEHypothesis = 8
    DISPID_SRCEPropertyNumberChange = 9
    DISPID_SRCEPropertyStringChange = 10
    DISPID_SRCEFalseRecognition = 11
    DISPID_SRCEInterference = 12
    DISPID_SRCERequestUI = 13
    DISPID_SRCERecognizerStateChange = 14
    DISPID_SRCEAdaptation = 15
    DISPID_SRCERecognitionForOtherContext = 16
    DISPID_SRCEAudioLevel = 17
    DISPID_SRCEEnginePrivate = 18


class DISPID_SpeechGrammarRule(IntFlag):
    DISPID_SGRAttributes = 1
    DISPID_SGRInitialState = 2
    DISPID_SGRName = 3
    DISPID_SGRId = 4
    DISPID_SGRClear = 5
    DISPID_SGRAddResource = 6
    DISPID_SGRAddState = 7


class DISPID_SpeechGrammarRules(IntFlag):
    DISPID_SGRsCount = 1
    DISPID_SGRsDynamic = 2
    DISPID_SGRsAdd = 3
    DISPID_SGRsCommit = 4
    DISPID_SGRsCommitAndSave = 5
    DISPID_SGRsFindRule = 6
    DISPID_SGRsItem = 0
    DISPID_SGRs_NewEnum = -4


class DISPID_SpeechGrammarRuleState(IntFlag):
    DISPID_SGRSRule = 1
    DISPID_SGRSTransitions = 2
    DISPID_SGRSAddWordTransition = 3
    DISPID_SGRSAddRuleTransition = 4
    DISPID_SGRSAddSpecialTransition = 5


class DISPID_SpeechGrammarRuleStateTransitions(IntFlag):
    DISPID_SGRSTsCount = 1
    DISPID_SGRSTsItem = 0
    DISPID_SGRSTs_NewEnum = -4


class DISPID_SpeechGrammarRuleStateTransition(IntFlag):
    DISPID_SGRSTType = 1
    DISPID_SGRSTText = 2
    DISPID_SGRSTRule = 3
    DISPID_SGRSTWeight = 4
    DISPID_SGRSTPropertyName = 5
    DISPID_SGRSTPropertyId = 6
    DISPID_SGRSTPropertyValue = 7
    DISPID_SGRSTNextState = 8


class DISPIDSPTSI(IntFlag):
    DISPIDSPTSI_ActiveOffset = 1
    DISPIDSPTSI_ActiveLength = 2
    DISPIDSPTSI_SelectionOffset = 3
    DISPIDSPTSI_SelectionLength = 4


class DISPID_SpeechRecoResult(IntFlag):
    DISPID_SRRRecoContext = 1
    DISPID_SRRTimes = 2
    DISPID_SRRAudioFormat = 3
    DISPID_SRRPhraseInfo = 4
    DISPID_SRRAlternates = 5
    DISPID_SRRAudio = 6
    DISPID_SRRSpeakAudio = 7
    DISPID_SRRSaveToMemory = 8
    DISPID_SRRDiscardResultInfo = 9


SPAUDIOSTATE = _SPAUDIOSTATE
SPSTREAMFORMATTYPE = SPWAVEFORMATTYPE


__all__ = [
    'SREHypothesis', 'SPCS_DISABLED', 'SAFT11kHz16BitStereo',
    'DISPID_SRCERecognizerStateChange', 'DISPID_SOTCreateInstance',
    'DISPID_SGRsCommit', 'SWTDeleted', 'DISPID_SOTGetStorageFileName',
    'SASPause', 'SAFTNonStandardFormat', 'SRTSMLTimeout',
    'DISPID_SVEStreamEnd', 'eLEXTYPE_PRIVATE20',
    'DISPID_SRAudioInput', 'DISPID_SPPEngineConfidence',
    'SPDKL_CurrentUser', 'SVP_20', 'DISPID_SGRSTWeight',
    'SPPHRASERULE', 'SAFT32kHz16BitStereo', 'eLEXTYPE_VENDORLEXICON',
    'DISPID_SGRSTs_NewEnum', 'DISPID_SRRTLength', 'SVPAlert',
    'SpeechAudioState', 'SPSMF_SRGS_SEMANTICINTERPRETATION_MS',
    'ISpeechRecognizer', 'DISPID_SRCEventInterests',
    'STSF_CommonAppData', 'DISPID_SGRsCount', 'DISPID_SGRsAdd',
    'ISpGrammarBuilder', 'SGRSTTWildcard', 'ISpeechFileStream',
    'SPRULESTATE', 'ISpeechPhraseProperty', 'SVP_2', 'SPEI_RESERVED2',
    'SpeechEmulationCompareFlags', 'SVEBookmark', 'SPAR_High',
    'DISPID_SVGetAudioOutputs', 'SSSPTRelativeToEnd', 'SP_VISEME_2',
    'SpeechAudioFormatGUIDText', 'DISPID_SBSSeek',
    'DISPID_SVEVoiceChange', 'eLEXTYPE_APP', 'eLEXTYPE_RESERVED10',
    'DISPID_SRSCurrentStreamNumber', 'SAFTTrueSpeech_8kHz1BitMono',
    'Speech_Max_Word_Length', 'DISPID_SRSNumberOfActiveRules',
    'SpeechPropertyResourceUsage', 'DISPID_SASCurrentDevicePosition',
    'SRAORetainAudio', 'SP_VISEME_21', 'DISPID_SRCEAudioLevel',
    'DISPID_SPEDisplayText', 'SAFT8kHz16BitStereo', 'SDTAudio',
    'SPSNoun', 'STCLocalServer', 'eLEXTYPE_MORPHOLOGY',
    'DISPID_SPRuleParent', 'SPEI_INTERFERENCE', 'eLEXTYPE_PRIVATE7',
    'DISPID_SPIStartTime', 'SPAR_Low', 'DISPID_SREmulateRecognition',
    'SBONone', 'SVEAudioLevel', 'DISPID_SPERetainedStreamOffset',
    'SpeechWordType', 'SECFNoSpecialChars', 'eLEXTYPE_RESERVED8',
    'SPSUnknown', 'DISPID_SpeechGrammarRuleState',
    'DISPID_SpeechRecoResult', 'SRERecognition',
    'SVESentenceBoundary', 'SRCS_Disabled', 'SP_VISEME_4',
    'ISpeechPhraseRule', 'SVP_0', 'DISPID_SPRuleNumberOfElements',
    'eLEXTYPE_PRIVATE13', 'SpeechCategoryRecognizers',
    'eLEXTYPE_PRIVATE19', 'ISpRecognizer2', 'SPGS_DISABLED',
    'SPPS_Function', 'SPSFunction', 'DISPID_SBSWrite',
    'SpeechRecognizerState', 'SPBO_AHEAD', 'Library',
    'DISPID_SWFEExtraData', 'SPEI_START_INPUT_STREAM',
    'SPEI_PROPERTY_NUM_CHANGE', 'DISPID_SRIsUISupported',
    'ISpeechObjectToken', 'eLEXTYPE_RESERVED9', 'SAFT8kHz8BitMono',
    'SLODynamic', 'DISPID_SASNonBlockingIO', 'eLEXTYPE_PRIVATE14',
    'DISPIDSPTSI', 'SPEI_FALSE_RECOGNITION', 'SLOStatic',
    'ISpPhoneConverter', 'SpeechDisplayAttributes', 'SVP_5',
    'DISPID_SVRate', 'SPINTERFERENCE_TOOLOUD',
    'ISpeechPhraseAlternate', 'SpeechFormatType',
    'SAFT32kHz8BitStereo', 'DISPID_SFSOpen', 'SSTTTextBuffer',
    'ISpeechPhraseElements', 'DISPID_SpeechPhraseAlternate',
    'DISPID_SRRSpeakAudio', 'DISPID_SOTCategory',
    'DISPID_SVDisplayUI', 'DISPID_SRGCmdSetRuleState',
    'SpeechTokenShellFolder', 'DISPID_SpeechPhraseRule',
    'DISPID_SVEViseme', 'SpStreamFormatConverter',
    'DISPID_SRRSaveToMemory', 'SpeechAudioProperties',
    'SPEI_SOUND_END', 'DISPID_SVSInputSentenceLength',
    'SPRECOCONTEXTSTATUS', 'SpUnCompressedLexicon',
    'SPEI_RECO_STATE_CHANGE', 'DISPID_SVSpeakStream', 'SP_VISEME_13',
    'ISpeechLexiconPronunciation', 'typelib_path',
    'eLEXTYPE_PRIVATE1', 'SPEI_ADAPTATION',
    'ISpeechGrammarRuleStateTransitions', 'SRADefaultToActive',
    'DISPID_SVSLastBookmarkId', 'SPPS_SuppressWord',
    'DISPID_SPRsCount', 'SECFEmulateResult', 'SPGS_ENABLED',
    'SDA_One_Trailing_Space', 'DISPID_SRDisplayUI',
    'DISPID_SGRsFindRule', 'DISPID_SPAStartElementInResult',
    'DISPID_SRCVoicePurgeEvent', 'DISPIDSPTSI_SelectionOffset',
    'DISPID_SDKCreateKey', 'SGRSTTTextBuffer', 'SPRST_INACTIVE',
    'SpeechGrammarTagUnlimitedDictation', 'DISPID_SWFEBitsPerSample',
    'ISpeechObjectTokens', 'SPSERIALIZEDRESULT',
    'SAFTADPCM_11kHzStereo', 'STSF_AppData',
    'DISPID_SpeechWaveFormatEx', 'SECFIgnoreCase',
    'ISpNotifyTranslator', '_ISpeechVoiceEvents', 'SAFT11kHz8BitMono',
    'SAFT24kHz16BitStereo', 'DISPID_SVGetVoices', 'SVP_16',
    'DISPID_SOTId', 'SLTUser', 'SPRS_ACTIVE', 'SpeechRuleState',
    'SECHighConfidence', 'DISPID_SPEs_NewEnum', 'SINone',
    'ISpXMLRecoResult', 'IInternetSecurityManager',
    'DISPID_SRCRetainedAudio', 'SAFTCCITT_uLaw_22kHzMono',
    'SpeechVoicePriority', 'SpCompressedLexicon', 'SITooLoud',
    'SAFTCCITT_uLaw_8kHzMono', 'DISPID_SPIGrammarId',
    'DISPID_SpeechLexiconWords', 'SpeechTokenContext',
    'SPEI_TTS_PRIVATE', 'SPWORDPRONOUNCEABLE',
    'DISPID_SAFGetWaveFormatEx', 'SPEI_VISEME', 'ISpeechRecoResult',
    'SAFT12kHz16BitMono', 'SPEI_REQUEST_UI',
    'DISPID_SABufferNotifySize', 'DISPID_SRRGetXMLResult',
    'SPBO_NONE', 'DISPID_SRCEEnginePrivate',
    'SpeechTokenKeyAttributes', 'eLEXTYPE_PRIVATE10',
    'SPBOOKMARKOPTIONS', 'SVSFlagsAsync', 'ISpeechGrammarRules',
    'SDTAll', 'SECLowConfidence', 'DISPID_SGRAddState',
    'SPCATEGORYTYPE', 'ISpeechAudio', 'SPWT_DISPLAY',
    'DISPID_SPAsCount', 'SPSHORTCUTPAIRLIST',
    'SGDSActiveWithAutoPause', 'SAFTGSM610_44kHzMono',
    'DISPID_SVESentenceBoundary', 'SpInProcRecoContext',
    'DISPID_SRCBookmark', 'SVPOver',
    'DISPID_SRCEPropertyStringChange', 'SREAdaptation',
    'DISPID_SPRuleFirstElement',
    'DISPID_SVAllowAudioOuputFormatChangesOnNextSet',
    'eLEXTYPE_PRIVATE6', 'SREBookmark', 'DISPID_SOTGetAttribute',
    'SPWORDPRONUNCIATION', 'DISPID_SPIGetText', 'ISpeechAudioFormat',
    'ISpeechPhraseProperties', 'SVP_15', 'DISPID_SPEAudioSizeBytes',
    'SREPropertyNumChange', 'SpPhoneticAlphabetConverter',
    'SpeechDiscardType', 'SPAR_Medium', 'ISpLexicon',
    'SpNullPhoneConverter', 'SDTDisplayText', 'SAFT48kHz16BitStereo',
    'DISPIDSPTSI_SelectionLength', 'SPRST_INACTIVE_WITH_PURGE',
    'SPRS_ACTIVE_WITH_AUTO_PAUSE', 'SPPHRASEREPLACEMENT',
    'SAFT32kHz16BitMono', 'SPVPRIORITY', 'SPEVENTENUM',
    'ISpNotifySource', 'SpeechTokenValueCLSID', 'DISPID_SAFType',
    'DISPID_SWFEChannels', 'SRSEIsSpeaking', 'STCInprocServer',
    'SREInterference', 'SVP_17', 'DISPID_SGRSAddRuleTransition',
    'SPBO_TIME_UNITS', 'IEnumSpObjectTokens',
    'DISPID_SRGCmdLoadFromObject', 'DISPID_SGRName', 'SRTAutopause',
    'DISPID_SpeechGrammarRuleStateTransition', 'SGDSInactive',
    'DISPID_SLPsItem', 'SPSLMA', 'SGSExclusive', 'SPVOICESTATUS',
    'DISPID_SPEDisplayAttributes', 'DISPID_SpeechPhraseReplacement',
    'SPEI_START_SR_STREAM', 'ISpObjectToken', 'SVP_6',
    'DISPID_SVResume', 'DISPID_SRGCmdLoadFromMemory',
    'DISPID_SVSyncronousSpeakTimeout', 'DISPID_SGRSTsItem',
    'DISPID_SPILanguageId', 'SpMMAudioEnum',
    'DISPID_SDKGetStringValue', 'DISPID_SPPValue',
    'SpeechVisemeFeature', 'DISPID_SFSClose', 'DISPID_SRCEBookmark',
    'ISpeechVoiceStatus', 'SPLEXICONTYPE', 'DISPID_SLWs_NewEnum',
    'SVSFIsNotXML', 'SREPhraseStart', 'DISPID_SPACommit',
    'DISPID_SOTDisplayUI', 'SAFT16kHz16BitStereo',
    'SAFTADPCM_22kHzStereo', 'STCInprocHandler', 'SPPS_LMA',
    'SGPronounciation', 'DISPID_SpeechPhraseRules', 'DISPID_SAStatus',
    'SpMMAudioIn', 'DISPID_SVEStreamStart',
    'DISPIDSPTSI_ActiveOffset', 'SITooSlow',
    'DISPID_SRGDictationUnload', 'SVEPrivate',
    'DISPID_SPERetainedSizeBytes', 'SRTExtendableParse',
    'ISpeechAudioBufferInfo', 'DISPID_SRGSetWordSequenceData',
    'SP_VISEME_20', 'SPPS_Unknown', 'DISPID_SpeechObjectToken',
    'ISpRecoCategory', 'SAFTGSM610_22kHzMono', 'DISPID_SPAs_NewEnum',
    'SWPUnknownWordPronounceable', 'DISPID_SPRuleName',
    'DISPID_SMSGetData', 'LONG_PTR', 'IStream', 'SPAUDIOOPTIONS',
    'DISPID_SpeechPhraseElement', 'eLEXTYPE_USER', 'STCAll', 'SPWORD',
    'SGLexical', 'DISPID_SRGReset', 'SpPhraseInfoBuilder',
    'SITooQuiet', 'ISpEventSink', 'DISPID_SRGIsPronounceable',
    'SpFileStream', 'SpeechCategoryAudioOut', 'ISpeechRecoGrammar',
    'DISPID_SRProfile', 'DISPID_SpeechPhraseBuilder', 'SDTRule',
    'ISpeechXMLRecoResult', 'DISPID_SPIEnginePrivateData',
    'DISPID_SRCERecognitionForOtherContext', 'SpeechLoadOption',
    'DISPID_SRCCmdMaxAlternates', 'DISPID_SDKDeleteKey',
    'SPEI_PHONEME', 'DISPID_SVSInputWordPosition',
    'DISPID_SVSLastStreamNumberQueued', 'SVP_21',
    'DISPID_SPRulesCount', 'SpeechDataKeyLocation',
    'ISpeechPhraseReplacement', 'DISPIDSPRG', 'SAFTADPCM_8kHzStereo',
    'DISPID_SRGState', 'DISPID_SRRDiscardResultInfo',
    'DISPID_SpeechPhraseInfo', 'DISPID_SPIEngineId',
    'DISPID_SPRFirstElement', 'STSF_FlagCreate', 'ISpPhrase',
    'SPWT_PRONUNCIATION', 'DISPID_SGRSTPropertyValue',
    'ISpMMSysAudio', 'SRESoundStart', 'SPEVENTSOURCEINFO',
    'ISpeechDataKey', 'SAFTCCITT_ALaw_44kHzMono', 'SP_VISEME_0',
    'DISPID_SRCESoundStart', 'DISPID_SOTCId', 'ISpPhraseAlt',
    'DISPID_SLGetGenerationChange', 'SPSERIALIZEDPHRASE',
    'SP_VISEME_17', 'SRSActive', 'ISpeechTextSelectionInformation',
    'SPXMLRESULTOPTIONS', 'SVP_14', 'SpeechAudioFormatType',
    'ISpeechObjectTokenCategory', 'ISpeechPhoneConverter',
    'SPSMF_SAPI_PROPERTIES', 'ISpVoice', 'SDKLCurrentConfig',
    'DISPID_SGRId', 'DISPID_SpeechLexiconPronunciation',
    'DISPID_SPIProperties', 'SpVoice', 'STSF_LocalAppData',
    'SDA_Two_Trailing_Spaces', 'SSSPTRelativeToStart',
    'SPEI_PHRASE_START', 'SP_VISEME_16',
    'DISPID_SOTMatchesAttributes', 'SAFT24kHz8BitStereo',
    'DISPID_SRCreateRecoContext', 'SPWP_KNOWN_WORD_PRONOUNCEABLE',
    'DISPID_SVStatus', 'SpeechPropertyResponseSpeed',
    'DISPID_SGRAttributes', 'SPRST_ACTIVE_ALWAYS',
    'DISPID_SPRuleEngineConfidence', 'SPGRAMMARWORDTYPE',
    'SVSFNLPSpeakPunc', 'DISPID_SOTCDefault',
    'DISPID_SRGSetTextSelection', 'DISPID_SRCERecognition',
    'ISpeechAudioStatus', '__MIDL___MIDL_itf_sapi_0000_0020_0001',
    'SINoSignal', 'DISPID_SRCEAdaptation', 'SPVPRI_NORMAL',
    'DISPID_SLAddPronunciationByPhoneIds', 'DISPID_SVSRunningState',
    'SPEI_MIN_SR', 'DISPID_SLWWord', 'SVSFParseSapi',
    'DISPID_SRRRecoContext', 'SPEI_SOUND_START', 'SDTProperty',
    'SAFT16kHz8BitStereo', 'SpeechVoiceSkipTypeSentence',
    'SpeechDictationTopicSpelling', 'DISPID_SRCRequestedUIType',
    'DISPID_SRCResume', 'SPSMF_UPS', 'DISPID_SRGetPropertyString',
    'DISPID_SDKEnumValues', 'DISPID_SPIRetainedSizeBytes',
    'DISPID_SPPFirstElement', 'SAFTCCITT_ALaw_44kHzStereo',
    'eLEXTYPE_PRIVATE2', 'DISPID_SRCCreateGrammar', 'ISpShortcut',
    'DISPID_SpeechRecoResult2', 'SSTTWildcard', 'SPPS_Verb',
    'DISPID_SLWType', 'DISPID_SpeechAudioBufferInfo', 'SDTAlternates',
    'SWTAdded', 'SpeechRunState', 'SASRun', 'DISPID_SVSpeak',
    'SPBINARYGRAMMAR', 'DISPID_SpeechAudioFormat', 'ISpRecoGrammar2',
    'SREStateChange', 'SPAS_PAUSE', 'SAFT44kHz8BitStereo',
    'SPEI_END_INPUT_STREAM', 'SPEI_MAX_TTS', 'SGDSActive', 'SFTInput',
    'DISPID_SPPs_NewEnum', 'SRESoundEnd', 'SRAONone',
    'ISpeechLexicon', 'SSFMOpenReadWrite', 'SpCustomStream',
    'DISPID_SVVoice', 'DISPID_SOTsItem', 'DISPID_SVAlertBoundary',
    'DISPID_SRCEStartStream', 'DISPID_SVGetProfiles', 'SVSFNLPMask',
    'DISPID_SMSAMMHandle', 'SP_VISEME_15', 'SLTApp', 'SPAUDIOSTATE',
    'SPFM_CREATE_ALWAYS', 'DISPID_SOTsCount', 'ISpeechPhraseRules',
    'WAVEFORMATEX', 'SAFTCCITT_ALaw_11kHzMono', 'SPWORDLIST',
    'eLEXTYPE_PRIVATE18', 'SGRSTTRule',
    'ISpeechLexiconPronunciations', 'DISPID_SRSSupportedLanguages',
    'DISPID_SpeechPhraseElements', 'DISPID_SPRuleChildren',
    'Speech_StreamPos_Asap', 'eLEXTYPE_PRIVATE11', 'SPPHRASEPROPERTY',
    'SRTStandard', 'DISPID_SMSADeviceId', 'SpeechVisemeType',
    'DISPID_SpeechVoiceEvent', 'DISPID_SGRs_NewEnum',
    'ISpeechRecognizerStatus', 'DISPID_SPELexicalForm',
    'SpeechCategoryAppLexicons', 'SPEI_RESERVED1',
    'tagSPPROPERTYINFO', 'SpeechEngineConfidence',
    'DISPID_SOTCEnumerateTokens',
    'SPINTERFERENCE_LATENCY_TRUNCATE_END', 'SREPropertyStringChange',
    'ISpeechPhraseReplacements', 'DISPID_SPAsItem',
    'SECNormalConfidence', 'DISPID_SRRPhraseInfo',
    'SAFTCCITT_ALaw_22kHzStereo', 'DISPID_SPEsCount',
    'ISpSerializeState', 'DISPID_SOTDataKey',
    'DISPID_SRCSetAdaptationData', 'SRERecoOtherContext',
    'SpeechCategoryPhoneConverters', 'SpeechRetainedAudioOptions',
    'SVF_Stressed', 'SPRULE', 'DISPID_SRCPause', 'DISPID_SVSkip',
    'SVEAllEvents', 'SRAExport', 'DISPID_SWFEBlockAlign',
    'SAFT11kHz8BitStereo', 'DISPID_SWFEFormatTag',
    'ISpObjectTokenCategory', 'SpeechBookmarkOptions',
    'SPEI_SR_PRIVATE', 'ISpRecoContext', 'SP_VISEME_5',
    'ISpeechCustomStream', 'DISPID_SpeechRecoContext',
    'SAFT44kHz16BitMono', 'SPAUDIOSTATUS', 'DISPID_SRRTimes',
    'SpeechWordPronounceable', 'SPADAPTATIONRELEVANCE',
    'ISpStreamFormatConverter', 'DISPID_SAFGuid',
    'DISPID_SpeechPhraseAlternates', 'DISPID_SPISaveToMemory',
    'DISPID_SPPId', 'SPINTERFERENCE', 'SPPARTOFSPEECH',
    'SREAudioLevel', 'eLEXTYPE_RESERVED7',
    'SpeechPropertyHighConfidenceThreshold', 'DISPID_SVSPhonemeId',
    'DISPID_SVEWord', 'DISPID_SVPriority', 'DISPID_SPIRule',
    'SPRS_INACTIVE', 'ISpeechLexiconWords',
    'SpeechStreamSeekPositionType', 'SPFM_CREATE',
    'DISPID_SpeechPhoneConverter', 'DISPID_SpeechObjectTokens',
    'SpeechPropertyNormalConfidenceThreshold', 'SVP_11',
    'SDKLDefaultLocation', 'DISPID_SRCRetainedAudioFormat',
    'DISPID_SVSLastBookmark', 'SP_VISEME_7', 'ISpRecoContext2',
    'DISPID_SOTCGetDataKey', 'DISPID_SVPause', 'DISPID_SLPLangId',
    'DISPID_SRRSetTextFeedback', 'DISPID_SGRSTType', 'SP_VISEME_8',
    'SPAS_CLOSED', 'SPGRAMMARSTATE', 'DISPID_SOTRemove',
    'SAFTCCITT_uLaw_22kHzStereo', 'DISPID_SRCEFalseRecognition',
    'SVP_19', 'SSFMCreateForWrite', 'SPRECOGNIZERSTATUS',
    'DISPID_SpeechRecognizer', 'SpeechPropertyLowConfidenceThreshold',
    'DISPID_SRAudioInputStream', 'DISPID_SpeechPhraseProperty',
    'SPEI_SR_RETAINEDAUDIO', 'SPAR_Unknown', 'SVP_3', 'SRCS_Enabled',
    'SpeechGrammarWordType', 'SREPrivate', 'DISPID_SpeechVoice',
    'DISPID_SRCCreateResultFromMemory', 'DISPID_SRGCmdSetRuleIdState',
    'SAFTGSM610_11kHzMono', 'DISPID_SRRAudioFormat',
    'DISPID_SVWaitUntilDone', 'DISPID_SRState', 'SpeechTokenKeyUI',
    'ISpeechBaseStream', 'DISPID_SPERequiredConfidence',
    'SPEI_SR_AUDIO_LEVEL', 'SDA_No_Trailing_Space',
    'SAFTADPCM_11kHzMono', 'DISPID_SLPsCount', 'DISPID_SRIsShared',
    'ISpeechRecoContext', 'SAFT16kHz8BitMono',
    'SPINTERFERENCE_LATENCY_WARNING',
    'DISPID_SpeechRecoContextEvents', 'SRSActiveAlways',
    'SpPhoneConverter', 'SPEI_RESERVED5',
    'SpeechPropertyComplexResponseSpeed', 'SpeechAllElements',
    'DISPID_SASFreeBufferSpace', 'SPCT_SLEEP', 'DISPID_SAEventHandle',
    'SPPS_RESERVED2', 'SREStreamEnd', 'SpeechGrammarState',
    'SPDKL_LocalMachine', 'DISPID_SRCRecognizer', 'SPSHORTCUTPAIR',
    'DISPID_SpeechLexiconWord', 'DISPID_SOTGetDescription',
    'DISPID_SDKDeleteValue', 'SPSHT_Unknown', 'DISPID_SCSBaseStream',
    'SPVPRI_OVER', 'SRSInactiveWithPurge', 'ISpeechMemoryStream',
    'SPSSuppressWord', 'DISPID_SRStatus',
    'DISPID_SPEActualConfidence', 'SPAUDIOBUFFERINFO',
    'SPEI_RESERVED6', 'DISPID_SRGDictationSetState',
    'DISPID_SRCERequestUI', 'SAFT8kHz16BitMono',
    'SAFT44kHz16BitStereo', 'SPWP_UNKNOWN_WORD_PRONOUNCEABLE',
    'SSTTDictation', 'DISPID_SVSVisemeId', 'DISPID_SLWPronunciations',
    'SBOPause', '_SPAUDIOSTATE', 'DISPID_SGRsItem',
    'DISPID_SpeechXMLRecoResult', 'DISPID_SPPConfidence',
    'DISPID_SVEventInterests', 'Speech_Max_Pron_Length',
    'SPWT_LEXICAL_NO_SPECIAL_CHARS', 'DISPID_SLAddPronunciation',
    'SpeechRegistryUserRoot', 'DISPID_SGRSTsCount',
    '__MIDL___MIDL_itf_sapi_0000_0020_0002', 'SpeechInterference',
    'ISpRecognizer3', 'Speech_StreamPos_RealTime',
    'SpTextSelectionInformation', 'DISPID_SRRAudio',
    'DISPID_SPPNumberOfElements', 'SPEI_TTS_BOOKMARK', 'SPCS_ENABLED',
    'SP_VISEME_9', 'SP_VISEME_12', 'SpeechAudioVolume',
    'SpNotifyTranslator', 'SPSEMANTICFORMAT', 'SPWT_LEXICAL',
    'DISPID_SPPName', 'SPFM_OPEN_READONLY', 'SVF_None',
    'DISPID_SPIAudioSizeTime', 'SPPHRASE', 'DISPID_SDKEnumKeys',
    'SpAudioFormat', 'DISPID_SWFEAvgBytesPerSec',
    'SAFTCCITT_uLaw_8kHzStereo', 'SSFMCreate', 'SPXRO_Alternates_SML',
    'SP_VISEME_11', 'SPCT_COMMAND', 'SSSPTRelativeToCurrentPosition',
    'SPINTERFERENCE_TOOQUIET', 'SAFT48kHz8BitMono',
    'eLEXTYPE_PRIVATE3', 'DISPID_SpeechFileStream',
    'SAFT12kHz16BitStereo', 'SAFT24kHz16BitMono',
    'SPRS_ACTIVE_USER_DELIMITED', 'SECFDefault', 'IEnumString',
    'SPEI_MAX_SR', 'SPEI_WORD_BOUNDARY', 'SPEI_SENTENCE_BOUNDARY',
    'SAFT11kHz16BitMono', 'SAFT12kHz8BitMono', 'ISpeechMMSysAudio',
    'SPEI_UNDEFINED', 'SPXRO_SML', 'SVSFIsFilename',
    'ISpResourceManager', 'SAFTCCITT_ALaw_8kHzMono', 'SPFM_NUM_MODES',
    'ISpRecognizer', 'SpeechCategoryAudioIn', 'SpeechCategoryVoices',
    'ISpeechPhraseAlternates', 'SAFTCCITT_uLaw_11kHzStereo',
    'DISPID_SRCEHypothesis', 'DISPID_SVSInputWordLength',
    'DISPID_SRAllowVoiceFormatMatchingOnNextSet', 'SpResourceManager',
    'SPAO_RETAIN_AUDIO', 'eWORDTYPE_DELETED',
    'DISPID_SOTIsUISupported', 'SpSharedRecoContext',
    'SPGS_EXCLUSIVE', 'SpShortcut', 'DISPID_SRGDictationLoad',
    'DISPID_SPIAudioStreamPosition', 'SREStreamStart',
    'DISPID_SVAudioOutputStream', 'DISPID_SPIAudioSizeBytes',
    'DISPID_SLWLangId', 'SPDKL_DefaultLocation', 'SPCT_SUB_DICTATION',
    'SpLexicon', 'DISPID_SGRSTNextState', 'SGRSTTWord',
    'DISPID_SRAllowAudioInputFormatChangesOnNextSet', 'ISpDataKey',
    'ISpeechGrammarRuleStateTransition', 'SPINTERFERENCE_TOOSLOW',
    'SpInprocRecognizer', 'SVSFPurgeBeforeSpeak', 'ISpProperties',
    'DISPID_SRCEInterference', 'DISPID_SDKGetBinaryValue',
    'DISPID_SVVolume', 'SPWF_SRENGINE', 'SRERequestUI',
    'SPINTERFERENCE_NOSIGNAL', 'SVSFVoiceMask', 'SVSFUnusedFlags',
    'ISpeechRecoResultTimes', 'ISpeechLexiconWord', 'DISPID_SLPType',
    'SREAllEvents', 'DISPID_SAFSetWaveFormatEx', 'SRAImport',
    'SREFalseRecognition', 'SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN',
    'SAFTGSM610_8kHzMono', 'SPINTERFERENCE_TOOFAST',
    'DISPID_SVSInputSentencePosition', 'DISPID_SPRuleConfidence',
    'DISPID_SGRClear', 'SPSVerb', 'DISPID_SOTRemoveStorageFileName',
    'SVSFParseAutodetect', 'DISPID_SPIReplacements',
    'DISPID_SPRulesItem', 'DISPID_SLPPhoneIds',
    'DISPID_SRRTTickCount', 'SpeechVoiceEvents',
    'tagSPTEXTSELECTIONINFO', 'Speech_Default_Weight',
    'DISPID_SGRSTPropertyId', 'SPINTERFERENCE_NONE',
    'DISPID_SLPs_NewEnum', 'ISpeechRecoResult2',
    'DISPID_SABufferInfo', 'SPEI_RECO_OTHER_CONTEXT',
    '_ISpeechRecoContextEvents', 'SGDisplay', 'DISPID_SLGenerationId',
    'ISpRecoGrammar', 'DISPID_SRRTOffsetFromStart',
    'eLEXTYPE_PRIVATE17', 'DISPID_SASCurrentSeekPosition',
    'SpeechPropertyAdaptationOn', 'DISPID_SGRSTRule',
    'SPTEXTSELECTIONINFO', 'SDA_Consume_Leading_Spaces',
    'SpeechGrammarTagDictation', 'DISPID_SMSSetData',
    'DISPID_SVEEnginePrivate', 'SpeechCategoryRecoProfiles',
    'SPSNotOverriden', 'SECFIgnoreWidth',
    'SAFTCCITT_uLaw_44kHzStereo', 'ISpeechResourceLoader',
    'ISpeechWaveFormatEx', 'SDKLCurrentUser',
    'SpeechGrammarRuleStateTransitionType', 'SpeechAddRemoveWord',
    'SVP_4', 'DISPID_SPRDisplayAttributes', 'SpeechEngineProperties',
    'DISPID_SRGCmdLoadFromResource', 'DISPID_SDKOpenKey',
    'DISPID_SPRules_NewEnum', 'DISPID_SpeechObjectTokenCategory',
    'SGLexicalNoSpecialChars', 'SpeechGrammarTagWildcard',
    'STCRemoteServer', 'DISPID_SWFESamplesPerSec',
    'DISPID_SPEAudioTimeOffset', 'DISPID_SpeechLexicon',
    'DISPID_SPCLangId', 'SpObjectTokenCategory', 'SpeechPartOfSpeech',
    'DISPID_SPPChildren', 'DISPID_SRCAudioInInterferenceStatus',
    'SVSFDefault', 'DISPID_SRSetPropertyNumber',
    'DISPID_SLRemovePronunciation', 'SpeechAudioFormatGUIDWave',
    'DISPID_SPEAudioStreamOffset', '__MIDL_IWinTypes_0009',
    'SPDATAKEYLOCATION', 'UINT_PTR', 'SVEPhoneme', 'SPLO_STATIC',
    'ISpeechVoice', 'DISPID_SPRs_NewEnum',
    'DISPID_SLRemovePronunciationByPhoneIds',
    'DISPID_SpeechGrammarRuleStateTransitions', 'SPEVENT',
    'SPSEMANTICERRORINFO', 'DISPID_SPEsItem', 'ISpObjectWithToken',
    'SPRECOSTATE', 'SpeechRuleAttributes', 'SINoise',
    'SAFTADPCM_8kHzMono', 'DISPID_SpeechGrammarRule',
    'DISPID_SRGCommit', 'SPRECORESULTTIMES',
    'DISPID_SRSetPropertyString', 'DISPID_SpeechGrammarRules',
    'SPFILEMODE', 'DISPID_SDKSetLongValue',
    'DISPID_SGRSAddWordTransition', 'DISPID_SLWsItem',
    'SpObjectToken', 'SAFTCCITT_ALaw_8kHzStereo', 'DISPID_SPRuleId',
    'ISpEventSource', 'SPBO_PAUSE', 'SPPS_Noun', 'eLEXTYPE_PRIVATE8',
    'SPPS_RESERVED1', 'SPLOADOPTIONS', 'SRAInterpreter',
    'ISpPhoneticAlphabetSelection', 'eLEXTYPE_RESERVED4',
    'eLEXTYPE_PRIVATE15', 'SPRST_NUM_STATES', 'SPSHT_NotOverriden',
    'DISPID_SGRSTransitions', 'SP_VISEME_6', 'SPPHRASEELEMENT',
    'SpMemoryStream', 'SVP_8', 'DISPID_SVIsUISupported',
    'DISPID_SRSClsidEngine', 'SGRSTTDictation', 'SVF_Emphasis',
    'eLEXTYPE_LETTERTOSOUND', 'SDKLLocalMachine',
    'DISPID_SGRsDynamic', 'DISPID_SPPsCount', 'SFTSREngine',
    'SPWAVEFORMATTYPE', 'SRTEmulated', 'SDTPronunciation',
    'SAFT48kHz16BitMono', 'SVEEndInputStream', 'SDTLexicalForm',
    'DISPID_SpeechDataKey', 'SAFTNoAssignedFormat',
    'DISPID_SLPSymbolic', 'SPEI_PROPERTY_STRING_CHANGE',
    'SPPS_NotOverriden', 'DISPID_SpeechAudioStatus',
    'DISPID_SRCEEndStream', 'DISPID_SpeechAudio',
    'SPEI_TTS_AUDIO_LEVEL', 'SPPROPERTYINFO', 'DISPID_SLGetWords',
    'DISPID_SRGetFormat', 'DISPID_SRCEPropertyNumberChange',
    'SGDSActiveUserDelimited', 'DISPID_SRCVoice',
    'SAFTExtendedAudioFormat', 'SpSharedRecognizer', 'SVP_9',
    'DISPID_SPPParent', 'DISPID_SPEPronunciation',
    'SPSTREAMFORMATTYPE', 'SRADynamic', 'SpeechVoiceSpeakFlags',
    'SAFTCCITT_uLaw_11kHzMono', 'SAFT24kHz8BitMono',
    'SpeechMicTraining', 'SAFTCCITT_ALaw_22kHzMono',
    'ISpeechPhraseElement', 'DISPID_SGRSRule', 'SDTReplacement',
    'DISPID_SRSAudioStatus', 'ISpeechPhraseInfoBuilder', 'SPAS_RUN',
    'DISPID_SADefaultFormat', 'DISPID_SRSCurrentStreamPosition',
    'DISPID_SpeechMMSysAudio', 'SPPS_RESERVED3', 'SPEI_SR_BOOKMARK',
    'DISPID_SPRNumberOfElements', 'SASClosed', 'SVEStartInputStream',
    'eLEXTYPE_RESERVED6', '_RemotableHandle', 'DISPID_SRGRecoContext',
    'SPWORDPRONUNCIATIONLIST', 'DISPID_SpeechPhraseReplacements',
    'SGSDisabled', 'DISPID_SVEPhoneme', 'eLEXTYPE_PRIVATE4',
    'DISPID_SGRInitialState', 'DISPID_SVEBookmark', 'SPAO_NONE',
    'ISpAudio', 'SAFT22kHz8BitMono', 'SAFTText',
    'DISPID_SPARecoResult', 'SPEI_END_SR_STREAM',
    'SpeechRegistryLocalMachineRoot', 'SRATopLevel',
    'SPFM_OPEN_READWRITE', 'eLEXTYPE_PRIVATE5', 'SGSEnabled',
    'SPSHT_EMAIL', 'ISpeechGrammarRule', 'SpeechLexiconType',
    'DISPID_SpeechPhraseProperties', 'SASStop',
    'SpeechSpecialTransitionType', 'DISPID_SGRAddResource',
    'eWORDTYPE_ADDED', 'SAFT44kHz8BitMono', 'DISPID_SAVolume',
    'eLEXTYPE_PRIVATE9', 'DISPID_SPRText', 'SPWORDTYPE',
    'SPEI_MIN_TTS', 'SP_VISEME_18', 'SRARoot', 'DISPID_SGRSTText',
    'DISPID_SOTCSetId', 'SPSHT_OTHER', 'SpeechRecoProfileProperties',
    'SP_VISEME_3', 'SPEI_VOICE_CHANGE', 'DISPID_SVGetAudioInputs',
    'SECFIgnoreKanaType', 'eLEXTYPE_PRIVATE16', 'DISPID_SPRsItem',
    'SPEI_ACTIVE_CATEGORY_CHANGED', 'SPCT_DICTATION',
    'eLEXTYPE_USER_SHORTCUT', 'DISPID_SABIEventBias',
    'SPPS_Interjection', 'DISPID_SRCESoundEnd', 'SPEI_RESERVED3',
    'DISPID_SMSALineId', 'DISPID_SVEAudioLevel',
    'SAFTCCITT_uLaw_44kHzMono', 'SPCONTEXTSTATE',
    'DISPID_SDKGetlongValue', 'DISPID_SVAudioOutput', 'SRTReSent',
    'SPRST_ACTIVE', 'SPINTERFERENCE_NOISE', 'DISPID_SOTs_NewEnum',
    'SVSFIsXML', 'DISPID_SRRGetXMLErrorInfo', 'SPPS_RESERVED4',
    'DISPID_SpeechVoiceStatus', 'SRSEDone',
    'DISPID_SRGetPropertyNumber', 'SAFTADPCM_44kHzMono',
    'DISPID_SBSRead', 'DISPID_SVSCurrentStreamNumber', 'SPWF_INPUT',
    'SAFT48kHz8BitStereo', 'DISPID_SRGCmdLoadFromProprietaryGrammar',
    'DISPID_SRGetRecognizers', 'DISPID_SPAPhraseInfo',
    'DISPID_SOTSetId', 'SPSMF_SRGS_SAPIPROPERTIES', 'SVP_13',
    'DISPID_SABIMinNotification', 'DISPID_SPCIdToPhone',
    'ISpeechRecoResultDispatch', 'DISPID_SRRTStreamTime',
    'DISPID_SVSpeakCompleteEvent', 'SVP_18', 'DISPID_SPIElements',
    'SAFT32kHz8BitMono', 'SP_VISEME_1', 'SAFT22kHz16BitStereo',
    'SPPS_Noncontent', 'DISPID_SpeechLexiconProns',
    'DISPID_SPEAudioSizeTime', 'DISPID_SASState',
    'DISPID_SPEEngineConfidence', 'DISPID_SpeechRecognizerStatus',
    'DISPID_SPPBRestorePhraseFromMemory', 'SVP_7', 'DISPID_SLWsCount',
    'SPSMF_SRGS_SEMANTICINTERPRETATION_W3C', 'SpeechTokenKeyFiles',
    'SpeechUserTraining', 'DISPIDSPTSI_ActiveLength', 'SRSInactive',
    'SAFT12kHz8BitStereo', 'SPDKL_CurrentConfig',
    'DISPID_SpeechMemoryStream', 'DISPID_SRGId',
    'DISPID_SLGetPronunciations', 'ISpPhoneticAlphabetConverter',
    'SPEI_RECOGNITION', 'SPCT_SUB_COMMAND', 'SP_VISEME_10', 'SVP_1',
    'SpeechRecognitionType', 'SPSModifier',
    'DISPID_SDKSetStringValue', 'SAFTADPCM_44kHzStereo',
    'DISPID_SPCPhoneToId', 'DISPID_SDKSetBinaryValue',
    'ISpNotifySink', 'DISPID_SpeechBaseStream', 'SpStream',
    'DISPID_SRGCmdLoadFromFile', 'SpeechStreamFileMode', 'SVEViseme',
    'DISPID_SRCEPhraseStart', 'SAFT22kHz8BitStereo',
    'DISPID_SRGRules', 'SPVPRI_ALERT', 'SPVISEMES',
    'DISPID_SpeechRecoResultTimes', 'SpeechVoiceCategoryTTSRate',
    'SAFT8kHz8BitStereo', 'DISPID_SGRsCommitAndSave',
    'SPSHORTCUTTYPE', 'ISpRecoResult', 'SpeechTokenIdUserLexicon',
    'SWPUnknownWordUnpronounceable', 'SpeechRecoEvents',
    'SPSInterjection', 'DISPID_SLPPartOfSpeech',
    'SAFTCCITT_ALaw_11kHzStereo', 'ISpeechGrammarRuleState',
    'SVSFParseMask', 'SVPNormal', 'SpeechRecoContextState',
    'SPEI_HYPOTHESIS', 'DISPID_SpeechCustomStream', 'SAFTDefault',
    'DISPID_SASetState', 'DISPID_SRRecognizer', 'SVSFParseSsml',
    'DISPID_SRCState', 'ISpeechPhraseInfo', 'DISPID_SPPsItem',
    'SAFT16kHz16BitMono', 'SP_VISEME_19', 'SVEVoiceChange',
    'SSFMOpenForRead', 'ISpStream', 'tagSTATSTG',
    'DISPID_SPANumberOfElementsInResult', 'SAFT22kHz16BitMono',
    'SVP_12', 'SPAS_STOP', 'SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE',
    'DISPID_SABIBufferSize', 'DISPID_SGRSAddSpecialTransition',
    'eLEXTYPE_PRIVATE12', 'DISPID_SGRSTPropertyName', 'SpMMAudioOut',
    'SVP_10', 'DISPID_SPIGetDisplayAttributes', 'SAFTADPCM_22kHzMono',
    'SPPS_Modifier', 'DISPID_SVSLastResult',
    'SWPKnownWordPronounceable', 'SpWaveFormatEx', 'SP_VISEME_14',
    'SVEWordBoundary', 'DISPID_SBSFormat', 'ISpStreamFormat',
    'SVSFPersistXML', 'IInternetSecurityMgrSite', 'SITooFast',
    'DISPID_SRRAlternates', 'SGRSTTEpsilon', 'SPLO_DYNAMIC'
]

