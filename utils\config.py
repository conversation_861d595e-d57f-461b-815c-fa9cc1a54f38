"""
Configuration management for AI Voice Assistant
Handles environment variables, settings, and application paths
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv

class Config:
    """Configuration manager for the voice assistant"""
    
    def __init__(self):
        """Initialize configuration by loading environment variables"""
        # Load environment variables from .env file
        load_dotenv()
        
        # Create necessary directories
        self._create_directories()
        
        # Setup logging
        self._setup_logging()
        
    def _create_directories(self):
        """Create necessary directories for the application"""
        directories = [
            "logs",
            "generated_images",
            "audio_recordings"
        ]
        
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_level = self.get('LOG_LEVEL', 'INFO')
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format=log_format,
            handlers=[
                logging.FileHandler('logs/assistant.log'),
                logging.StreamHandler()
            ]
        )
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value from environment variables"""
        return os.getenv(key, default)
    
    def get_bool(self, key: str, default: bool = False) -> bool:
        """Get boolean configuration value"""
        value = self.get(key, str(default)).lower()
        return value in ('true', '1', 'yes', 'on')
    
    def get_int(self, key: str, default: int = 0) -> int:
        """Get integer configuration value"""
        try:
            return int(self.get(key, default))
        except (ValueError, TypeError):
            return default
    
    def get_float(self, key: str, default: float = 0.0) -> float:
        """Get float configuration value"""
        try:
            return float(self.get(key, default))
        except (ValueError, TypeError):
            return default
    
    @property
    def api_keys(self) -> Dict[str, Optional[str]]:
        """Get all API keys"""
        return {
            'grok': self.get('GROK_API_KEY'),
            'huggingface': self.get('HUGGINGFACE_API_KEY')
        }
    
    @property
    def email_config(self) -> Dict[str, Optional[str]]:
        """Get email configuration"""
        return {
            'gmail_email': self.get('GMAIL_EMAIL'),
            'gmail_password': self.get('GMAIL_APP_PASSWORD'),
            'outlook_email': self.get('OUTLOOK_EMAIL'),
            'outlook_password': self.get('OUTLOOK_PASSWORD')
        }
    
    @property
    def voice_settings(self) -> Dict[str, Any]:
        """Get voice assistant settings"""
        return {
            'wake_word': self.get('WAKE_WORD', 'hey nova').lower(),
            'assistant_name': self.get('ASSISTANT_NAME', 'Nova'),
            'voice_rate': self.get_int('VOICE_RATE', 200),
            'voice_volume': self.get_float('VOICE_VOLUME', 0.9),
            'microphone_sensitivity': self.get_float('MICROPHONE_SENSITIVITY', 0.5)
        }
    
    @property
    def app_paths(self) -> Dict[str, str]:
        """Get application paths for launching programs"""
        username = os.getenv('USERNAME', 'User')
        
        return {
            'chrome': self.get('CHROME_PATH', 
                r'C:\Program Files\Google\Chrome\Application\chrome.exe'),
            'vscode': self.get('VSCODE_PATH', 
                rf'C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe'),
            'notepad': self.get('NOTEPAD_PATH', 
                r'C:\Windows\System32\notepad.exe'),
            'calculator': self.get('CALCULATOR_PATH', 
                r'C:\Windows\System32\calc.exe'),
            'explorer': r'C:\Windows\explorer.exe',
            'cmd': r'C:\Windows\System32\cmd.exe',
            'powershell': r'C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe'
        }
    
    def validate_config(self) -> Dict[str, bool]:
        """Validate configuration and return status of each component"""
        validation_results = {}
        
        # Check API keys
        api_keys = self.api_keys
        validation_results['grok_api'] = bool(api_keys['grok'])
        validation_results['huggingface_api'] = bool(api_keys['huggingface'])
        
        # Check email configuration
        email_config = self.email_config
        validation_results['gmail_config'] = bool(
            email_config['gmail_email'] and email_config['gmail_password']
        )
        validation_results['outlook_config'] = bool(
            email_config['outlook_email'] and email_config['outlook_password']
        )
        
        # Check application paths
        app_paths = self.app_paths
        validation_results['app_paths'] = all(
            os.path.exists(path) for path in app_paths.values() 
            if os.path.isabs(path)
        )
        
        return validation_results

# Global configuration instance
config = Config()
