"""
Basic test of voice assistant components
Tests what's working without requiring all dependencies
"""

import os
import sys
import time
from datetime import datetime

print("🤖 Voice Assistant - Basic Component Test")
print("=" * 50)

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_configuration():
    """Test configuration module"""
    print("⚙️ Testing configuration...")
    
    try:
        from utils.config import config
        print("✅ Configuration module loaded")
        
        # Test voice settings
        voice_settings = config.voice_settings
        print(f"✅ Assistant name: {voice_settings.get('assistant_name', 'Unknown')}")
        print(f"✅ Wake word: {voice_settings.get('wake_word', 'Unknown')}")
        
        # Test API keys (without showing actual values)
        api_keys = config.api_keys
        available_apis = [key for key, value in api_keys.items() if value]
        print(f"✅ Configured APIs: {available_apis if available_apis else 'None'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_text_to_speech():
    """Test text-to-speech if available"""
    print("\n🔊 Testing text-to-speech...")
    
    try:
        import pyttsx3
        print("✅ pyttsx3 available")
        
        # Try to initialize TTS engine
        engine = pyttsx3.init()
        print("✅ TTS engine initialized")
        
        # Test speech (comment out if you don't want audio)
        # engine.say("Hello, this is a test of the text to speech system.")
        # engine.runAndWait()
        print("✅ TTS test completed (speech disabled for testing)")
        
        return True
        
    except ImportError:
        print("❌ pyttsx3 not available - install with: pip install pyttsx3")
        return False
    except Exception as e:
        print(f"❌ TTS test failed: {e}")
        return False

def test_system_actions():
    """Test system actions if available"""
    print("\n🖥️ Testing system actions...")
    
    try:
        import psutil
        print("✅ psutil available")
        
        # Test system info
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        print(f"✅ CPU usage: {cpu_percent}%")
        print(f"✅ Memory usage: {memory.percent}%")
        
        # Test battery if available
        try:
            battery = psutil.sensors_battery()
            if battery:
                print(f"✅ Battery: {battery.percent}%")
            else:
                print("ℹ️ No battery detected (desktop computer)")
        except:
            print("ℹ️ Battery info not available")
        
        return True
        
    except ImportError:
        print("❌ psutil not available - install with: pip install psutil")
        return False
    except Exception as e:
        print(f"❌ System actions test failed: {e}")
        return False

def test_responder_module():
    """Test the responder module"""
    print("\n🗣️ Testing responder module...")
    
    try:
        from core.responder import VoiceResponder
        print("✅ Responder module loaded")
        
        # Try to initialize (but don't speak)
        responder = VoiceResponder()
        print("✅ Responder initialized")
        
        # Test text cleaning
        test_text = "This is a **test** with `code` and special characters!"
        cleaned = responder._clean_text(test_text)
        print(f"✅ Text cleaning: '{test_text}' -> '{cleaned}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Responder test failed: {e}")
        return False

def test_actions_module():
    """Test the actions module"""
    print("\n⚡ Testing actions module...")
    
    try:
        from core.actions import SystemActions
        print("✅ Actions module loaded")
        
        actions = SystemActions()
        print("✅ Actions initialized")
        
        # Test system info
        system_info = actions.get_system_info()
        if 'error' not in system_info:
            print(f"✅ System info retrieved")
            print(f"   CPU: {system_info['cpu']['usage_percent']:.1f}%")
            print(f"   Memory: {system_info['memory']['usage_percent']:.1f}%")
        
        # Test battery
        battery_info = actions.get_battery_status()
        print(f"✅ Battery info: {battery_info['message']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Actions test failed: {e}")
        return False

def test_ai_module():
    """Test the AI module"""
    print("\n🧠 Testing AI module...")
    
    try:
        from core.ai import AIAssistant
        print("✅ AI module loaded")
        
        ai = AIAssistant()
        print("✅ AI assistant initialized")
        
        # Test available models
        available_models = ai.get_available_models()
        print(f"✅ Available models: {available_models}")
        
        # Test simple question (will fail without API keys)
        if any(available_models.values()):
            print("ℹ️ API keys configured - AI responses available")
        else:
            print("ℹ️ No API keys configured - AI responses not available")
        
        return True
        
    except Exception as e:
        print(f"❌ AI test failed: {e}")
        return False

def run_comprehensive_test():
    """Run all available tests"""
    print("🧪 Running comprehensive component test...\n")
    
    tests = [
        ("Configuration", test_configuration),
        ("Text-to-Speech", test_text_to_speech),
        ("System Actions", test_system_actions),
        ("Responder Module", test_responder_module),
        ("Actions Module", test_actions_module),
        ("AI Module", test_ai_module)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("-" * 30)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed >= total * 0.7:
        print("\n🎉 Most components are working!")
        print("\nNext steps:")
        print("1. Install missing dependencies:")
        print("   pip install vosk pyaudio transformers torch openai")
        print("2. Add API keys to .env file")
        print("3. Download Vosk model for speech recognition")
        print("4. Run: python main.py")
    else:
        print("\n⚠️ Several components need attention.")
        print("Check the failed tests above and install missing dependencies.")

def main():
    """Main test function"""
    try:
        run_comprehensive_test()
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"Test failed: {e}")

if __name__ == "__main__":
    main()
