"""
Test script to check which imports are working
"""

import sys
import os

print("🔍 Testing imports for Voice Assistant...")
print(f"Python version: {sys.version}")
print(f"Current directory: {os.getcwd()}")
print("-" * 50)

# Test basic imports
try:
    import logging
    print("✅ logging - OK")
except ImportError as e:
    print(f"❌ logging - FAILED: {e}")

try:
    from datetime import datetime
    print("✅ datetime - OK")
except ImportError as e:
    print(f"❌ datetime - FAILED: {e}")

try:
    from typing import Dict, Any, Optional
    print("✅ typing - OK")
except ImportError as e:
    print(f"❌ typing - FAILED: {e}")

# Test third-party imports
print("\n📦 Testing third-party packages...")

try:
    import pyttsx3
    print("✅ pyttsx3 - OK")
except ImportError as e:
    print(f"❌ pyttsx3 - FAILED: {e}")

try:
    import pyaudio
    print("✅ pyaudio - OK")
except ImportError as e:
    print(f"❌ pyaudio - FAILED: {e}")

try:
    import vosk
    print("✅ vosk - OK")
except ImportError as e:
    print(f"❌ vosk - FAILED: {e}")

try:
    import numpy as np
    print("✅ numpy - OK")
except ImportError as e:
    print(f"❌ numpy - FAILED: {e}")

try:
    import requests
    print("✅ requests - OK")
except ImportError as e:
    print(f"❌ requests - FAILED: {e}")

try:
    import psutil
    print("✅ psutil - OK")
except ImportError as e:
    print(f"❌ psutil - FAILED: {e}")

try:
    import pyautogui
    print("✅ pyautogui - OK")
except ImportError as e:
    print(f"❌ pyautogui - FAILED: {e}")

try:
    from dotenv import load_dotenv
    print("✅ python-dotenv - OK")
except ImportError as e:
    print(f"❌ python-dotenv - FAILED: {e}")

try:
    import transformers
    print("✅ transformers - OK")
except ImportError as e:
    print(f"❌ transformers - FAILED: {e}")

try:
    import torch
    print("✅ torch - OK")
except ImportError as e:
    print(f"❌ torch - FAILED: {e}")

try:
    from openai import OpenAI
    print("✅ openai - OK")
except ImportError as e:
    print(f"❌ openai - FAILED: {e}")

# Test local imports
print("\n🏠 Testing local modules...")

try:
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from utils.config import config
    print("✅ utils.config - OK")
    print(f"   Assistant name: {config.voice_settings.get('assistant_name', 'Unknown')}")
except Exception as e:
    print(f"❌ utils.config - FAILED: {e}")

try:
    from utils.speech_tools import AudioProcessor
    print("✅ utils.speech_tools - OK")
except Exception as e:
    print(f"❌ utils.speech_tools - FAILED: {e}")

try:
    from core.responder import VoiceResponder
    print("✅ core.responder - OK")
except Exception as e:
    print(f"❌ core.responder - FAILED: {e}")

try:
    from core.listener import VoiceListener
    print("✅ core.listener - OK")
except Exception as e:
    print(f"❌ core.listener - FAILED: {e}")

try:
    from core.actions import SystemActions
    print("✅ core.actions - OK")
except Exception as e:
    print(f"❌ core.actions - FAILED: {e}")

print("\n" + "=" * 50)
print("Import test completed!")
print("If any imports failed, install them with:")
print("pip install <package_name>")
