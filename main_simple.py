"""
Simplified AI Voice Assistant - Main Application
This version works without external dependencies for testing
"""

import os
import sys
import time
import logging
from datetime import datetime

print("🤖 AI Voice Assistant - Simple Test Version")
print("=" * 50)

# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/assistant_simple.log')
    ]
)

logger = logging.getLogger(__name__)

class SimpleVoiceAssistant:
    """Simplified Voice Assistant for testing"""
    
    def __init__(self):
        """Initialize the simple voice assistant"""
        self.assistant_name = "Nova"
        self.is_running = False
        
        print(f"Initializing {self.assistant_name}...")
        logger.info(f"{self.assistant_name} Simple Voice Assistant initialized")
        
        # Test basic functionality
        self.test_basic_functions()
    
    def test_basic_functions(self):
        """Test basic functions without external dependencies"""
        print("\n🧪 Testing basic functions...")
        
        try:
            # Test time/date
            now = datetime.now()
            current_time = now.strftime("%I:%M %p")
            current_date = now.strftime("%A, %B %d, %Y")
            print(f"✅ Time/Date: {current_time} on {current_date}")
            
            # Test system info (basic)
            try:
                import platform
                system_info = {
                    'system': platform.system(),
                    'release': platform.release(),
                    'machine': platform.machine(),
                    'processor': platform.processor()
                }
                print(f"✅ System: {system_info['system']} {system_info['release']}")
            except Exception as e:
                print(f"⚠️ System info error: {e}")
            
            # Test file operations
            try:
                os.makedirs('logs', exist_ok=True)
                os.makedirs('generated_images', exist_ok=True)
                os.makedirs('audio_recordings', exist_ok=True)
                print("✅ Directories created successfully")
            except Exception as e:
                print(f"⚠️ Directory creation error: {e}")
            
            # Test configuration loading
            self.test_configuration()
            
            print("✅ Basic functions test completed")
            
        except Exception as e:
            print(f"❌ Basic functions test failed: {e}")
            logger.error(f"Basic functions test failed: {e}")
    
    def test_configuration(self):
        """Test configuration loading"""
        print("\n⚙️ Testing configuration...")
        
        try:
            # Check if .env file exists
            if os.path.exists('.env'):
                print("✅ .env file found")
                
                # Try to load it manually (without python-dotenv)
                with open('.env', 'r') as f:
                    lines = f.readlines()
                    config_count = 0
                    for line in lines:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            config_count += 1
                    print(f"✅ Found {config_count} configuration entries")
            else:
                print("⚠️ .env file not found")
                
                # Check if .env.example exists
                if os.path.exists('.env.example'):
                    print("✅ .env.example found - you can copy this to .env")
                else:
                    print("❌ .env.example not found")
            
        except Exception as e:
            print(f"❌ Configuration test failed: {e}")
    
    def simulate_voice_commands(self):
        """Simulate processing voice commands"""
        print("\n🎤 Simulating voice commands...")
        
        test_commands = [
            "what time is it",
            "what's the date",
            "open notepad",
            "what's my battery status",
            "take a screenshot",
            "hello how are you"
        ]
        
        for command in test_commands:
            print(f"\n🗣️ User: {command}")
            response = self.process_command(command)
            print(f"🤖 {self.assistant_name}: {response}")
            time.sleep(1)
    
    def process_command(self, command: str) -> str:
        """Process a command and return response"""
        command_lower = command.lower().strip()
        
        try:
            if 'time' in command_lower:
                now = datetime.now()
                current_time = now.strftime("%I:%M %p")
                return f"It's currently {current_time}."
            
            elif 'date' in command_lower:
                now = datetime.now()
                current_date = now.strftime("%A, %B %d, %Y")
                return f"Today is {current_date}."
            
            elif 'open' in command_lower or 'launch' in command_lower:
                app_name = command_lower.replace('open', '').replace('launch', '').strip()
                return f"I would open {app_name} if all dependencies were installed."
            
            elif 'battery' in command_lower:
                return "I would check your battery status if psutil was installed."
            
            elif 'screenshot' in command_lower:
                return "I would take a screenshot if pyautogui was installed."
            
            else:
                return "I would process this with AI if the API keys were configured."
                
        except Exception as e:
            logger.error(f"Error processing command: {e}")
            return "I encountered an error processing that command."
    
    def start(self):
        """Start the simple voice assistant"""
        try:
            print(f"\n🚀 Starting {self.assistant_name} Simple Voice Assistant...")
            self.is_running = True
            
            # Simulate the assistant running
            print("\n📝 This is a simplified version for testing.")
            print("The full version requires these packages:")
            print("- vosk (speech recognition)")
            print("- pyttsx3 (text-to-speech)")
            print("- pyaudio (audio processing)")
            print("- transformers (emotion detection)")
            print("- requests (API calls)")
            print("- psutil (system monitoring)")
            print("- pyautogui (computer control)")
            print("- python-dotenv (configuration)")
            
            # Run simulation
            self.simulate_voice_commands()
            
            print(f"\n✅ {self.assistant_name} simulation completed!")
            print("\nTo install all dependencies, run:")
            print("pip install -r requirements.txt")
            
            print("\nTo run the full version:")
            print("1. Install dependencies")
            print("2. Copy .env.example to .env")
            print("3. Add your API keys to .env")
            print("4. Run: python main.py")
            
        except Exception as e:
            logger.error(f"Error starting assistant: {e}")
            print(f"Error: {e}")
        finally:
            self.is_running = False

def main():
    """Main entry point for simple version"""
    try:
        assistant = SimpleVoiceAssistant()
        assistant.start()
        
    except KeyboardInterrupt:
        print("\nShutting down...")
    except Exception as e:
        print(f"Fatal error: {e}")
        logger.error(f"Fatal error: {e}")
    finally:
        print("\nGoodbye!")

if __name__ == "__main__":
    main()
